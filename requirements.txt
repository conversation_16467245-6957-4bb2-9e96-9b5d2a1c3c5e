# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o requirements.txt
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   httpx
    #   openai
asgiref==3.9.1
    # via
    #   django
    #   django-cors-headers
boto3==1.39.15
    # via
    #   api-django (pyproject.toml)
    #   django-storages
botocore==1.39.15
    # via
    #   boto3
    #   s3transfer
cachecontrol==0.14.3
    # via firebase-admin
cachetools==5.5.2
    # via google-auth
certifi==2025.7.14
    # via
    #   httpcore
    #   httpx
    #   requests
    #   sentry-sdk
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via uvicorn
cryptography==45.0.5
    # via pyjwt
distro==1.9.0
    # via openai
django==5.2.4
    # via
    #   api-django (pyproject.toml)
    #   django-cors-headers
    #   django-extensions
    #   django-ninja
    #   django-redis
    #   django-safedelete
    #   django-storages
    #   sentry-sdk
django-cors-headers==4.7.0
    # via api-django (pyproject.toml)
django-extensions==4.1
    # via api-django (pyproject.toml)
django-hosts==7.0.0
    # via api-django (pyproject.toml)
django-ninja==1.4.3
    # via api-django (pyproject.toml)
django-redis==6.0.0
    # via api-django (pyproject.toml)
django-safedelete==1.4.1
    # via api-django (pyproject.toml)
django-storages==1.14.6
    # via api-django (pyproject.toml)
dnspython==2.7.0
    # via email-validator
dotenv==0.9.9
    # via api-django (pyproject.toml)
email-validator==2.2.0
    # via pydantic
factory-boy==3.3.3
    # via api-django (pyproject.toml)
faker==37.4.2
    # via factory-boy
firebase-admin==7.0.0
    # via api-django (pyproject.toml)
google-api-core==2.25.1
    # via
    #   firebase-admin
    #   google-cloud-core
    #   google-cloud-firestore
    #   google-cloud-storage
google-auth==2.40.3
    # via
    #   google-api-core
    #   google-cloud-core
    #   google-cloud-firestore
    #   google-cloud-storage
google-cloud-core==2.4.3
    # via
    #   google-cloud-firestore
    #   google-cloud-storage
google-cloud-firestore==2.21.0
    # via firebase-admin
google-cloud-storage==3.2.0
    # via firebase-admin
google-crc32c==1.7.1
    # via
    #   google-cloud-storage
    #   google-resumable-media
google-resumable-media==2.7.2
    # via google-cloud-storage
googleapis-common-protos==1.70.0
    # via
    #   google-api-core
    #   grpcio-status
grpcio==1.74.0
    # via
    #   google-api-core
    #   grpcio-status
grpcio-status==1.74.0
    # via google-api-core
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
h2==4.2.0
    # via httpx
hpack==4.1.0
    # via h2
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via
    #   api-django (pyproject.toml)
    #   firebase-admin
    #   openai
    #   upstash-vector
hyperframe==6.1.0
    # via h2
idna==3.10
    # via
    #   anyio
    #   email-validator
    #   httpx
    #   requests
jiter==0.10.0
    # via openai
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
msgpack==1.1.1
    # via cachecontrol
numpy==2.3.2
    # via pgvector
openai==1.97.1
    # via api-django (pyproject.toml)
packaging==25.0
    # via django-safedelete
pgvector==0.4.1
    # via api-django (pyproject.toml)
pillow==11.3.0
    # via api-django (pyproject.toml)
proto-plus==1.26.1
    # via
    #   google-api-core
    #   google-cloud-firestore
protobuf==6.31.1
    # via
    #   google-api-core
    #   google-cloud-firestore
    #   googleapis-common-protos
    #   grpcio-status
    #   proto-plus
psycopg2-binary==2.9.10
    # via api-django (pyproject.toml)
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycparser==2.22
    # via cffi
pydantic==2.11.7
    # via
    #   api-django (pyproject.toml)
    #   django-ninja
    #   openai
pydantic-core==2.33.2
    # via pydantic
pyjwt==2.10.1
    # via
    #   api-django (pyproject.toml)
    #   firebase-admin
python-dateutil==2.9.0.post0
    # via botocore
python-dotenv==1.1.1
    # via dotenv
redis==6.2.0
    # via
    #   api-django (pyproject.toml)
    #   django-redis
requests==2.32.4
    # via
    #   cachecontrol
    #   google-api-core
    #   google-cloud-storage
rsa==4.9.1
    # via google-auth
s3transfer==0.13.1
    # via boto3
sentry-sdk==2.34.1
    # via api-django (pyproject.toml)
six==1.17.0
    # via python-dateutil
sniffio==1.3.1
    # via
    #   anyio
    #   openai
sqlparse==0.5.3
    # via django
tqdm==4.67.1
    # via openai
typing-extensions==4.14.1
    # via
    #   anyio
    #   openai
    #   pydantic
    #   pydantic-core
    #   typing-inspection
typing-inspection==0.4.1
    # via pydantic
tzdata==2025.2
    # via faker
upstash-vector==0.8.0
    # via api-django (pyproject.toml)
urllib3==2.5.0
    # via
    #   botocore
    #   requests
    #   sentry-sdk
uvicorn==0.35.0
    # via api-django (pyproject.toml)
whitenoise==6.9.0
    # via api-django (pyproject.toml)
