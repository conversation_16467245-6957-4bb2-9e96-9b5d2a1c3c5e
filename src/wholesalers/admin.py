from django.contrib import admin
from django.http import HttpResponse
from django.urls import path
from django.shortcuts import render, get_object_or_404
from django.utils import timezone
from datetime import datetime
from dateutil.relativedelta import relativedelta
import csv
from .models import RegionMinCharge, Wholesaler, Item, InventoryTransaction


class RegionMinChargeInline(admin.TabularInline):
    model = RegionMinCharge
    extra = 1
    raw_id_fields = ("region",)
    autocomplete_fields = ["region"]


class WholesalerAdmin(admin.ModelAdmin):
    list_display = (
        "title",
        "username",
        "category",
        "user",
        "created_at",
    )
    list_filter = ("category", "created_at")
    search_fields = ("title", "username", "user__username", "user__email")
    raw_id_fields = ("user",)
    readonly_fields = ("created_at", "updated_at")
    inlines = [RegionMinChargeInline]
    fieldsets = (
        (None, {"fields": ("title", "username", "user", "category")}),
        (
            "Media",
            {
                "fields": ("logo", "background_image"),
            },
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at", "deleted_at"),
                "classes": ("collapse",),
            },
        ),
    )

    def get_urls(self):
        """Add custom URLs for CSV export"""
        urls = super().get_urls()
        custom_urls = [
            path(
                "<int:wholesaler_id>/export-orders/",
                self.admin_site.admin_view(self.export_orders_view),
                name="wholesaler_export_orders",
            ),
            path(
                "<int:wholesaler_id>/export-orders/download/",
                self.admin_site.admin_view(self.download_orders_csv),
                name="wholesaler_download_orders_csv",
            ),
        ]
        return custom_urls + urls

    def export_orders_view(self, request, wholesaler_id):
        """View to show date range form for CSV export"""
        wholesaler = get_object_or_404(Wholesaler, id=wholesaler_id)

        # Default to last month
        end_date = timezone.now().date()
        start_date = end_date - relativedelta(months=1)

        context = {
            "wholesaler": wholesaler,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "title": f"Export Orders for {wholesaler.title}",
        }

        return render(request, "admin/wholesalers/export_orders.html", context)

    def download_orders_csv(self, request, wholesaler_id):
        """Generate and download CSV file of orders for the wholesaler"""
        wholesaler = get_object_or_404(Wholesaler, id=wholesaler_id)

        # Get date range from request
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")

        # Parse dates
        try:
            if start_date:
                start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
            else:
                start_date = timezone.now().date() - relativedelta(months=1)

            if end_date:
                end_date = datetime.strptime(end_date, "%Y-%m-%d").date()
            else:
                end_date = timezone.now().date()
        except ValueError:
            # If date parsing fails, use default
            end_date = timezone.now().date()
            start_date = end_date - relativedelta(months=1)

        # Query orders for the wholesaler within date range
        from stores.models import Order

        orders = (
            Order.objects.filter(
                wholesaler=wholesaler,
                deleted_at__isnull=True,
                created_at__date__gte=start_date,
                created_at__date__lte=end_date,
            )
            .select_related("store", "store__owner")
            .order_by("-created_at")
        )

        # Create CSV response
        response = HttpResponse(content_type="text/csv")
        filename = f"orders_{wholesaler.username}_{start_date}_{end_date}.csv"
        response["Content-Disposition"] = f'attachment; filename="{filename}"'

        writer = csv.writer(response)

        # Write CSV header
        writer.writerow(
            [
                "Order ID",
                "Store Name",
                "Store Owner",
                "Store Owner Phone",
                "Order Date",
                "Status",
                "Products Total Price",
                "Fees",
                "Total Price",
                "Total Quantity",
                "Delivery Date",
                "Completed At",
                "Final Completed Price",
            ]
        )

        # Write order data
        for order in orders:
            writer.writerow(
                [
                    order.id,
                    order.store.name,
                    order.store.owner.get_full_name() or order.store.owner.username,
                    getattr(order.store.owner, "phone", "") or "",
                    order.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    order.get_status_display(),
                    str(order.products_total_price),
                    str(order.fees),
                    str(order.total_price),
                    order.products_total_quantity,
                    order.deliver_at.strftime("%Y-%m-%d %H:%M:%S")
                    if order.deliver_at
                    else "",
                    order.completed_at.strftime("%Y-%m-%d %H:%M:%S")
                    if order.completed_at
                    else "",
                    str(order.final_completed_price)
                    if order.final_completed_price
                    else "",
                ]
            )

        return response

    def change_view(self, request, object_id, form_url="", extra_context=None):
        """Add export button to the change form"""
        extra_context = extra_context or {}
        extra_context["show_export_orders"] = True
        extra_context["export_orders_url"] = (
            f"/admin/wholesalers/wholesaler/{object_id}/export-orders/"
        )
        return super().change_view(request, object_id, form_url, extra_context)

    # def get_queryset(self, request):
    #     return (
    #         super()
    #         .get_queryset(request)
    #         .prefetch_related("user")
    #         .filter(deleted_at__isnull=True)
    #     )


class InventoryTransactionInline(admin.TabularInline):
    model = InventoryTransaction
    extra = 1
    readonly_fields = ("created_at",)
    fields = ("transaction_type", "quantity", "notes", "created_at")


class ItemAdmin(admin.ModelAdmin):
    list_display = (
        "product",
        "wholesaler",
        "base_price",
        "inventory_count",
        "price_expiry",
    )
    list_filter = (
        "wholesaler",
        "price_expiry",
        "created_at",
    )
    search_fields = (
        "product__name",
        "product__barcode",
        "wholesaler__title",
        "wholesaler__username",
    )
    raw_id_fields = ("wholesaler", "product")
    autocomplete_fields = ["product"]
    readonly_fields = ("created_at", "updated_at")
    inlines = [InventoryTransactionInline]
    fieldsets = (
        (None, {"fields": ("wholesaler", "product")}),
        (
            "Pricing & Inventory",
            {
                "fields": (
                    "base_price",
                    "inventory_count",
                    "minimum_order_quantity",
                    "maximum_order_quantity",
                ),
            },
        ),
        (
            "Dates",
            {
                "fields": ("price_expiry", "expires_at"),
            },
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at", "deleted_at"),
                "classes": ("collapse",),
            },
        ),
    )


class InventoryTransactionAdmin(admin.ModelAdmin):
    list_display = ("item", "transaction_type", "quantity", "created_at")
    list_filter = ("transaction_type", "created_at")
    search_fields = (
        "item__product__name",
        "item__product__barcode",
        "item__wholesaler__title",
        "notes",
    )
    raw_id_fields = ("item",)
    readonly_fields = ("created_at", "updated_at")
    fieldsets = (
        (None, {"fields": ("item", "transaction_type", "quantity", "notes")}),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at", "deleted_at"),
                "classes": ("collapse",),
            },
        ),
    )


# Register all models with the admin site
admin.site.register(Wholesaler, WholesalerAdmin)
admin.site.register(Item, ItemAdmin)
admin.site.register(InventoryTransaction, InventoryTransactionAdmin)
