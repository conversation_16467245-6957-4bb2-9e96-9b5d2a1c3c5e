# Generated by Django 5.2.1 on 2025-07-25 12:23

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0006_category_category_deleted_at_idx_and_more'),
        ('wholesalers', '0010_item_maximum_order_quantity_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='regionmincharge',
            name='wholesalers_region__d345a5_idx',
        ),
        migrations.RenameIndex(
            model_name='item',
            new_name='item_wholesaler_product_idx',
            old_name='wholesalers_wholesa_dda3f7_idx',
        ),
        migrations.RenameIndex(
            model_name='regionmincharge',
            new_name='rgnminchg_whlslr_rgn_idx',
            old_name='wholesalers_wholesa_092d33_idx',
        ),
        migrations.AddIndex(
            model_name='item',
            index=models.Index(fields=['deleted_at'], name='item_deleted_at_idx'),
        ),
        migrations.AddIndex(
            model_name='item',
            index=models.Index(fields=['wholesaler', 'deleted_at'], name='item_wholesaler_deleted_idx'),
        ),
        migrations.AddIndex(
            model_name='item',
            index=models.Index(fields=['product', 'deleted_at'], name='item_product_deleted_idx'),
        ),
        migrations.AddIndex(
            model_name='item',
            index=models.Index(fields=['inventory_count'], name='item_inventory_count_idx'),
        ),
        migrations.AddIndex(
            model_name='item',
            index=models.Index(fields=['inventory_count', 'deleted_at'], name='item_inventory_deleted_idx'),
        ),
        migrations.AddIndex(
            model_name='regionmincharge',
            index=models.Index(fields=['deleted_at'], name='regionmincharge_deleted_at_idx'),
        ),
        migrations.AddIndex(
            model_name='regionmincharge',
            index=models.Index(fields=['region', 'deleted_at'], name='rgnminchg_rgn_del_idx'),
        ),
        migrations.AddIndex(
            model_name='wholesaler',
            index=models.Index(fields=['deleted_at'], name='wholesaler_deleted_at_idx'),
        ),
        migrations.AddIndex(
            model_name='wholesaler',
            index=models.Index(fields=['user', 'deleted_at'], name='wholesaler_user_deleted_idx'),
        ),
        migrations.AddIndex(
            model_name='wholesaler',
            index=models.Index(fields=['category', 'deleted_at'], name='whlslr_category_del_idx'),
        ),
    ]
