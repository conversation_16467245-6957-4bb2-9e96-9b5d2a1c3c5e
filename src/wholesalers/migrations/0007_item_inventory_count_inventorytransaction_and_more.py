# Generated by Django 5.2 on 2025-05-07 22:31

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('wholesalers', '0006_remove_item_pricing_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='item',
            name='inventory_count',
            field=models.IntegerField(default=0, help_text='Current inventory count for this item', validators=[django.core.validators.MinValueValidator(0)]),
        ),
        migrations.CreateModel(
            name='InventoryTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('ADDITION', 'Addition'), ('SUBTRACTION', 'Subtraction')], help_text='Type of inventory transaction (addition or subtraction)', max_length=20)),
                ('quantity', models.IntegerField(help_text='Quantity added or removed from inventory', validators=[django.core.validators.MinValueValidator(1)])),
                ('notes', models.TextField(blank=True, help_text='Optional notes about this inventory transaction', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventory_transactions', to='wholesalers.item')),
            ],
            options={
                'verbose_name': 'Inventory Transaction',
                'verbose_name_plural': 'Inventory Transactions',
            },
        ),
        migrations.DeleteModel(
            name='ItemRegionPrice',
        ),
        migrations.AddIndex(
            model_name='inventorytransaction',
            index=models.Index(fields=['item', 'transaction_type'], name='wholesalers_item_id_59f6d0_idx'),
        ),
        migrations.AddIndex(
            model_name='inventorytransaction',
            index=models.Index(fields=['created_at'], name='wholesalers_created_281a52_idx'),
        ),
    ]
