"""
Signal handlers for the products app.

This module contains signal handlers that are triggered on various product-related events.
"""

import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.conf import settings
import openai
from django.contrib.postgres.search import SearchVector
from django.db.models import F

from .models import Product
from .utils import generate_search_vector, generate_embedding

# Configure logger
logger = logging.getLogger(__name__)

# Configure OpenAI API
openai.api_key = settings.OPENAI_API_KEY


# generate embedding for product on update
@receiver(post_save, sender=Product)
def generate_embedding_on_update(sender, instance, **kwargs):
    Product.objects.filter(pk=instance.pk).update(
        search_vector=(
            SearchVector("name", weight="A", config="arabic")
            + SearchVector("title", weight="A", config="arabic")
            + SearchVector("description", weight="B", config="arabic")
        )
    )
