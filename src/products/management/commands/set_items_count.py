from django.core.management.base import BaseCommand
from django.db.models import Count
from products.models import Product
from wholesalers.models import Item


class Command(BaseCommand):
    help = "Set items count for all products"

    def handle(self, *args, **options):
        # Get all item counts in a single query using aggregation
        item_counts = (
            Item.objects.values("product_id")
            .annotate(count=Count("id"))
            .values_list("product_id", "count")
        )

        # Convert to dictionary for fast lookup
        item_count_dict = dict(item_counts)

        # Get all products
        products = Product.objects.filter(id__in=item_count_dict.keys()).all()

        # Update products in batches
        batch_size = 1000
        updated_count = 0

        for i in range(0, len(products), batch_size):
            batch = products[i : i + batch_size]

            # Update items_count for each product in the batch
            for product in batch:
                product.items_count = item_count_dict.get(product.id, 0)

            # Bulk update the batch
            Product.objects.bulk_update(batch, ["items_count"])
            updated_count += len(batch)

            self.stdout.write(
                self.style.SUCCESS(f"Updated {updated_count}/{len(products)} products")
            )

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully updated items_count for {len(products)} products"
            )
        )
