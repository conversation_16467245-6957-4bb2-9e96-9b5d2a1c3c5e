"""
API endpoints for the Flutter app's Home screen.
"""

from typing import List, Dict, Any, Optional
from django.core.cache import cache
from .models import Product, Company, Category, Region
from .schemas import (
    ProductWithPricingOut,
    ProductPriceInfo,
    CompanyOut,
    CategoryOut,
)
from .views import _product_to_schema, _company_to_schema, _category_to_schema
from wholesalers.models import Item, RegionMinCharge
from wholesalers.schemas import WholesalerOut as WholesalerOutSchema
from django.conf import settings
from core.custom_router import CustomRouter

router = CustomRouter(tags=["home"])


def _get_region_hierarchy(region_id: int) -> List[int]:
    """
    Get a list of region IDs including the target region and all its parent regions.
    This allows filtering for wholesalers that serve the region or any parent region.
    """
    try:
        region = Region.objects.get(id=region_id, deleted_at__isnull=True)
    except Region.DoesNotExist:
        return []

    region_ids = [region_id]
    current_region = region

    # Traverse up the parent hierarchy
    while current_region.parent:
        region_ids.append(current_region.parent.id)
        current_region = current_region.parent

    return region_ids


def _get_products_by_region(region_id: Optional[int] = None, base_queryset=None):
    """
    Helper function to filter products based on region.
    Returns products from wholesalers that serve the region or its parent regions.
    """
    if base_queryset is None:
        base_queryset = Product.objects.filter(deleted_at__isnull=True)

    # Base filter for products with active wholesaler items
    products_filter = base_queryset.filter(
        wholesalers__deleted_at__isnull=True,
        wholesalers__inventory_count__gt=0,
    )

    # If region_id is provided, add region-based filtering
    if region_id is not None:
        region_ids = _get_region_hierarchy(region_id)
        if region_ids:
            # Filter products from wholesalers that serve the region or its parent regions
            products_filter = products_filter.filter(
                wholesalers__wholesaler__region_min_charge__region_id__in=region_ids,
                wholesalers__wholesaler__region_min_charge__deleted_at__isnull=True,
            )

    return products_filter.distinct()


def _get_product_pricing(product_id: int) -> Dict[str, Any]:
    """
    Helper function to get pricing information for a product from all wholesalers.
    Returns a dictionary with the lowest price and all other prices.
    """
    # Get all active items for this product from wholesalers with inventory > 0
    items = (
        Item.objects.filter(
            product_id=product_id,
            deleted_at__isnull=True,
            inventory_count__gt=0,
            wholesaler__deleted_at__isnull=True,
        )
        .select_related("wholesaler")
        .order_by("base_price")
    )

    if not items:
        return {
            "lowest_price": None,
            "lowest_price_wholesaler": None,
            "other_prices": [],
            "price_range": None,
        }

    # Convert items to price info
    price_info = []
    for item in items:
        price_info.append(
            {
                "price": item.base_price,
                "wholesaler": item.wholesaler,
                "inventory_count": item.inventory_count,
                "price_expiry": item.price_expiry,
            }
        )

    # Get the lowest price (first item since we ordered by base_price)
    lowest_price = price_info[0]["price"]
    lowest_price_wholesaler = price_info[0]["wholesaler"]

    # Other prices are all prices except the lowest one
    other_prices = price_info

    # Calculate price range
    min_price = min(p["price"] for p in price_info)
    max_price = max(p["price"] for p in price_info)

    return {
        "lowest_price": lowest_price,
        "lowest_price_wholesaler": lowest_price_wholesaler,
        "other_prices": other_prices,
        "price_range": {"min": min_price, "max": max_price},
    }


def _enrich_product_with_pricing(product_data: dict) -> dict:
    """
    Helper function to add pricing information to a product dictionary.
    """
    pricing = _get_product_pricing(product_data["id"])

    # Convert wholesaler to schema
    lowest_price_wholesaler = None
    if pricing["lowest_price_wholesaler"]:
        lowest_price_wholesaler = WholesalerOutSchema(
            id=pricing["lowest_price_wholesaler"].id,
            category=pricing["lowest_price_wholesaler"].category,
            title=pricing["lowest_price_wholesaler"].title,
            username=pricing["lowest_price_wholesaler"].username,
            logo_url=pricing["lowest_price_wholesaler"].logo.url
            if pricing["lowest_price_wholesaler"].logo
            else None,
            background_image_url=pricing["lowest_price_wholesaler"].background_image.url
            if pricing["lowest_price_wholesaler"].background_image
            else None,
            created_at=pricing["lowest_price_wholesaler"].created_at,
        )

    # Convert other prices to schema
    other_prices = []
    for price in pricing["other_prices"]:
        wholesaler = WholesalerOutSchema(
            id=price["wholesaler"].id,
            category=price["wholesaler"].category,
            title=price["wholesaler"].title,
            username=price["wholesaler"].username,
            logo_url=price["wholesaler"].logo.url if price["wholesaler"].logo else None,
            background_image_url=price["wholesaler"].background_image.url
            if price["wholesaler"].background_image
            else None,
            created_at=price["wholesaler"].created_at,
        )

        other_prices.append(
            ProductPriceInfo(
                price=price["price"],
                wholesaler=wholesaler,
                inventory_count=price["inventory_count"],
                price_expiry=price["price_expiry"],
            )
        )

    # Return the enriched product data
    return {
        **product_data,
        "lowest_price": pricing["lowest_price"],
        "lowest_price_wholesaler": lowest_price_wholesaler,
        "other_prices": other_prices,
        "price_range": pricing["price_range"],
    }


@router.get("/popular", response=List[ProductWithPricingOut])
def get_popular_products(request, limit: int = 10, region_id: Optional[int] = None):
    """
    Get popular products with pricing information from wholesalers.
    Currently returns newest products as a fallback, sorted by lowest price.
    Results are cached for 12 hours.

    Args:
        limit: Maximum number of products to return
        region_id: Optional region ID to filter products by wholesaler region
    """
    cache_key = f"home_popular_products_{limit}_{region_id}"
    if not settings.DEBUG:
        cached_result = cache.get(cache_key)

        if cached_result is not None:
            return cached_result

    # Get products with at least one active price from wholesalers
    products = _get_products_by_region(region_id).order_by("-created_at")[:limit]

    # Convert products to schema, enrich with pricing, and filter out products without prices
    result = []
    for product in products:
        product_data = _product_to_schema(product)
        enriched_product = _enrich_product_with_pricing(product_data)
        # Only include products that have at least one price
        if enriched_product["lowest_price"] is not None:
            result.append(ProductWithPricingOut(**enriched_product))

    # Sort by lowest price
    result.sort(key=lambda x: x.lowest_price)

    # Cache for 12 hours
    cache.set(cache_key, result, 60 * 60 * 12)
    return result


@router.get("/new-arrivals", response=List[ProductWithPricingOut])
def get_new_products(request, limit: int = 10, region_id: Optional[int] = None):
    """
    Get newly added products.
    Results are cached for 6 hours.

    Args:
        limit: Maximum number of products to return
        region_id: Optional region ID to filter products by wholesaler region
    """
    cache_key = f"home_new_products_{limit}_{region_id}"
    if not settings.DEBUG:
        cached_result = cache.get(cache_key)

        if cached_result is not None:
            return cached_result

    # Get products with at least one active price from wholesalers
    products = _get_products_by_region(region_id).order_by("-created_at")[:limit]

    # Convert products to schema, enrich with pricing, and filter out products without prices
    result = []
    for product in products:
        product_data = _product_to_schema(product)
        enriched_product = _enrich_product_with_pricing(product_data)
        # Only include products that have at least one price
        if enriched_product["lowest_price"] is not None:
            result.append(ProductWithPricingOut(**enriched_product))

    # Cache for 6 hours
    cache.set(cache_key, result, 60 * 60 * 6)
    return result


@router.get("/best-sellers", response=List[ProductWithPricingOut])
def get_best_selling_products(
    request, limit: int = 10, region_id: Optional[int] = None
):
    """
    Get best selling products based on order history.
    Results are cached for 24 hours.

    Args:
        limit: Maximum number of products to return
        region_id: Optional region ID to filter products by wholesaler region
    """
    from stores.models import OrderItem
    from django.db.models import Sum

    cache_key = f"home_best_sellers_{limit}_{region_id}"
    if not settings.DEBUG:
        cached_result = cache.get(cache_key)

        if cached_result is not None:
            return cached_result

    # Get product IDs and their total quantities sold, ordered by quantity
    best_selling = (
        OrderItem.objects.values("product_id")
        .annotate(total_quantity=Sum("quantity"))
        .order_by("-total_quantity")
        .values_list("product_id", flat=True)[:limit]
    )

    # Get the actual product objects with at least one active price, filtered by region
    base_queryset = Product.objects.filter(id__in=best_selling, deleted_at__isnull=True)
    products = _get_products_by_region(region_id, base_queryset).in_bulk(
        field_name="id"
    )

    # Process products in the order of best selling, filter out those without prices
    result = []
    for product_id in best_selling:
        if product_id in products:
            product_data = _product_to_schema(products[product_id])
            enriched_product = _enrich_product_with_pricing(product_data)
            # Only include products that have at least one price
            if enriched_product["lowest_price"] is not None:
                result.append(ProductWithPricingOut(**enriched_product))
                # Stop once we have enough products
                if len(result) >= limit:
                    break

    # Cache for 24 hours
    cache.set(cache_key, result, 60 * 60 * 24)
    return result


@router.get("/top-rated", response=List[ProductWithPricingOut])
def get_top_rated_products(request, limit: int = 10, region_id: Optional[int] = None):
    """
    Get top rated products based on reviews.
    Currently returns best selling products as a fallback.
    Results are cached for 24 hours.

    Args:
        limit: Maximum number of products to return
        region_id: Optional region ID to filter products by wholesaler region
    """
    # TODO: Implement actual rating system
    # For now, return best selling products as a fallback
    return get_best_selling_products(request, limit, region_id)


@router.get("/featured", response=List[ProductWithPricingOut])
def get_featured_products(request, limit: int = 10, region_id: Optional[int] = None):
    """
    Get featured products.
    Currently returns newest products as a fallback.
    Results are cached for 12 hours.

    Args:
        limit: Maximum number of products to return
        region_id: Optional region ID to filter products by wholesaler region
    """
    cache_key = f"home_featured_products_{limit}_{region_id}"
    if not settings.DEBUG:
        cached_result = cache.get(cache_key)

        if cached_result is not None:
            return cached_result

    # Get products with at least one active price from wholesalers
    products = _get_products_by_region(region_id).order_by("-created_at")[:limit]

    # Convert products to schema, enrich with pricing, and filter out products without prices
    result = []
    for product in products:
        product_data = _product_to_schema(product)
        enriched_product = _enrich_product_with_pricing(product_data)
        # Only include products that have at least one price
        if enriched_product["lowest_price"] is not None:
            result.append(ProductWithPricingOut(**enriched_product))

    # Cache for 12 hours
    cache.set(cache_key, result, 60 * 60 * 12)
    return result


@router.get("/discounted", response=List[ProductWithPricingOut])
def get_discounted_products(request, limit: int = 10, region_id: Optional[int] = None):
    """
    Get products with active discounts.
    Results are cached for 1 hour.

    Args:
        limit: Maximum number of products to return
        region_id: Optional region ID to filter products by wholesaler region
    """
    cache_key = f"home_discounted_products_{limit}_{region_id}"
    if not settings.DEBUG:
        cached_result = cache.get(cache_key)

        if cached_result is not None:
            return cached_result

    # TODO: Implement discount functionality when discount fields are added to Product model
    # For now, return regular products as a fallback
    products = _get_products_by_region(region_id).order_by("-created_at")[:limit]

    # Convert products to schema, enrich with pricing, and filter out products without prices
    result = []
    for product in products:
        product_data = _product_to_schema(product)
        enriched_product = _enrich_product_with_pricing(product_data)
        # Only include products that have at least one price
        if enriched_product["lowest_price"] is not None:
            result.append(ProductWithPricingOut(**enriched_product))

    # Cache for 1 hour (discounts might change frequently)
    cache.set(cache_key, result, 60 * 60)
    return result


@router.get("/brands", response=List[CompanyOut])
def get_brands(request, limit: int = 10):
    """
    Get a list of brands/companies.
    Results are cached for 24 hours.
    """
    cache_key = f"home_brands_{limit}"
    if not settings.DEBUG:
        cached_result = cache.get(cache_key)

        if cached_result is not None:
            return cached_result

    companies = Company.objects.filter(deleted_at__isnull=True).order_by("name")[:limit]

    result = [_company_to_schema(company) for company in companies]

    # Cache for 24 hours
    cache.set(cache_key, result, 60 * 60 * 24)
    return result


@router.get("/categories", response=List[CategoryOut])
def get_categories(request, limit: int = 10):
    """
    Get a list of categories.
    Results are cached for 24 hours.
    """
    cache_key = f"home_categories_{limit}"
    if not settings.DEBUG:
        cached_result = cache.get(cache_key)

        if cached_result is not None:
            return cached_result

    categories = Category.objects.filter(
        deleted_at__isnull=True,
    ).order_by("name")[:limit]

    result = [_category_to_schema(category) for category in categories]

    # Cache for 24 hours
    cache.set(cache_key, result, 60 * 60 * 24)
    return result
