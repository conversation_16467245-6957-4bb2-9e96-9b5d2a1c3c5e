"""
URL configuration for accounts app
"""

from ninja import Router

from accounts.views.auth_views import router as auth_router
from accounts.views.main_views import router as main_router

# Create a parent router for all accounts endpoints
router = Router()

# Include sub-routers
router.add_router("", auth_router)  # Auth endpoints at /accounts/...
router.add_router("", main_router)  # Main endpoints at /accounts/...
