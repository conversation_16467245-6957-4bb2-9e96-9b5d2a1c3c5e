{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Active Orders Management{% endblock %}

{% block extrahead %}
    <link rel="stylesheet" type="text/css" href="{% static 'admin/css/active_orders.css' %}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="admin-active-orders">
    <h1>Active Orders Management</h1>

    <!-- Messages -->
    {% if messages %}
        <ul class="messages">
            {% for message in messages %}
                <li class="{{ message.tags }}">{{ message }}</li>
            {% endfor %}
        </ul>
    {% endif %}

    <!-- Summary Stats -->
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-number">{{ total_orders }}</div>
            <div class="stat-label">Total Active Orders</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ current_page_count }}</div>
            <div class="stat-label">On This Page</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ total_pages }}</div>
            <div class="stat-label">Total Pages</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ pending_count|default:0 }}</div>
            <div class="stat-label">Pending Orders</div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
        <form method="get" id="filters-form">
            <div class="filters-row">
                <div class="filter-group">
                    <label for="search">Search Orders</label>
                    <input type="text" 
                           id="search" 
                           name="search" 
                           value="{{ filters.search|default:'' }}" 
                           placeholder="Search by store, user, or order details..."
                           autocomplete="off">
                </div>

                <div class="filter-group">
                    <label for="wholesaler">Filter by Wholesaler</label>
                    <select id="wholesaler" name="wholesaler">
                        <option value="">All Wholesalers</option>
                        {% for wholesaler in wholesalers %}
                        <option value="{{ wholesaler.id }}" {% if filters.wholesaler == wholesaler.id|stringformat:"s" %}selected{% endif %}>
                            {{ wholesaler.title }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <div class="filter-group">
                    <label for="status">Filter by Status</label>
                    <select id="status" name="status">
                        <option value="">All Statuses</option>
                        {% for status_value, status_display in status_choices %}
                        <option value="{{ status_value }}" {% if filters.status == status_value %}selected{% endif %}>
                            {{ status_display }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <div class="filter-group">
                    <label for="date_from">From Date</label>
                    <input type="date" 
                           id="date_from" 
                           name="date_from" 
                           value="{{ filters.date_from|default:'' }}">
                </div>

                <div class="filter-group">
                    <label for="date_to">To Date</label>
                    <input type="date" 
                           id="date_to" 
                           name="date_to" 
                           value="{{ filters.date_to|default:'' }}">
                </div>

                <div class="filter-buttons">
                    <button type="submit" class="filter-btn filter-btn-primary">
                        Apply Filters
                    </button>
                    <a href="{% url 'admin:stores_activeorder_changelist' %}" class="filter-btn filter-btn-secondary">
                        Clear All
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Utility Actions -->
    <div class="utility-actions">
        <button onclick="exportToCSV()" class="utility-btn">
            📊 Export CSV
        </button>
        <button onclick="printOrders()" class="utility-btn">
            🖨️ Print Orders
        </button>
        <button onclick="refreshPage()" class="utility-btn">
            🔄 Refresh Page
        </button>
    </div>

    <!-- Results Table -->
    <div class="results">
        {% if orders %}
            <table>
                <thead>
                    <tr>
                        <th>Order ID</th>
                        <th>Store Information</th>
                        <th>Wholesaler Information</th>
                        <th>Contact Numbers</th>
                        <th>Order Details</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order in orders %}
                    <tr>
                        <td>
                            <a href="{% url 'admin:stores_order_change' order.id %}">
                                #{{ order.id }}
                            </a>
                        </td>
                        <td>
                            <div class="store-info">
                                <strong>{{ order.store.name }}</strong>
                                <small>👤 {{ order.store.owner.get_full_name|default:order.store.owner.username }}</small>
                                <small>📍 {{ order.store.address|default:"No address provided" }}</small>
                            </div>
                        </td>
                        <td>
                            <div class="wholesaler-info">
                                <strong>{{ order.wholesaler.title }}</strong>
                                <small>👨‍💼 {{ order.wholesaler.user.get_full_name|default:order.wholesaler.user.username }}</small>
                                <small>@{{ order.wholesaler.user.username }}</small>
                            </div>
                        </td>
                        <td>
                            <div class="phone-numbers">
                                <div class="phone-item">
                                    <strong>Store:</strong>
                                    <span class="phone-number">{{ order.store.owner.phone|default:"Not available" }}</span>
                                    {% if order.store.owner.phone %}
                                    <button class="copy-btn" 
                                            data-phone="{{ order.store.owner.phone }}"
                                            title="Copy store phone number">
                                        📋
                                    </button>
                                    {% endif %}
                                </div>
                                <div class="phone-item">
                                    <strong>Wholesaler:</strong>
                                    <span class="phone-number">{{ order.wholesaler.user.phone|default:"Not available" }}</span>
                                    {% if order.wholesaler.user.phone %}
                                    <button class="copy-btn" 
                                            data-phone="{{ order.wholesaler.user.phone }}"
                                            title="Copy wholesaler phone number">
                                        📋
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="order-details">
                                <div>
                                    <strong>Total:</strong> 
                                    <span class="price">${{ order.total_price|floatformat:2 }}</span>
                                </div>
                                <div>
                                    <strong>Items:</strong> 
                                    <span>{{ order.orderitem_set.count }} item{{ order.orderitem_set.count|pluralize }}</span>
                                </div>
                                <div>
                                    <strong>Status:</strong> 
                                    <span class="status-badge status-{{ order.status|lower }}">
                                        {{ order.get_status_display }}
                                    </span>
                                </div>
                                <div>
                                    <strong>Created:</strong> 
                                    <time datetime="{{ order.created_at|date:'c' }}">
                                        {{ order.created_at|date:'M d, Y H:i' }}
                                    </time>
                                </div>
                                {% if order.deliver_at %}
                                <div>
                                    <strong>Delivery:</strong> 
                                    <time datetime="{{ order.deliver_at|date:'c' }}">
                                        {{ order.deliver_at|date:'M d, Y H:i' }}
                                    </time>
                                </div>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="actions">
                                <a href="{% url 'admin:stores_order_change' order.id %}" 
                                   title="Edit Order">
                                    ✏️ Edit
                                </a>
                                <a href="{% url 'admin:stores_order_change' order.id %}" 
                                   title="View Details">
                                    👁️ View
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <div class="no-results">
                <p>🔍 No active orders found matching your criteria.</p>
                <a href="{% url 'admin:stores_activeorder_changelist' %}">
                    Clear filters and show all orders
                </a>
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
        <div class="pagination">
            <div class="step-links">
                {% if page_obj.has_previous %}
                    <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page=1" 
                       title="First page">
                        ⏮️ First
                    </a>
                    <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" 
                       title="Previous page">
                        ⬅️ Previous
                    </a>
                {% endif %}

                <span class="current">
                    📄 Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </span>

                {% if page_obj.has_next %}
                    <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" 
                       title="Next page">
                        Next ➡️
                    </a>
                    <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.paginator.num_pages }}" 
                       title="Last page">
                        Last ⏭️
                    </a>
                {% endif %}
            </div>
        </div>
    {% endif %}
</div>

<script src="{% static 'admin/js/active_orders.js' %}"></script>
{% endblock %}