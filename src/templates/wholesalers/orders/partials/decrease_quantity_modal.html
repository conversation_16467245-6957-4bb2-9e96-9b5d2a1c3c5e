<!-- Decrease Quantity Modal -->
<div class="modal fade" id="decreaseQuantityModal" tabindex="-1" aria-labelledby="decreaseQuantityModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="decreaseQuantityModalLabel">
                    <i class="fas fa-minus-circle me-2"></i>
                    تقليل كمية المنتج
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <i class="fas fa-boxes text-warning fa-3x mb-3"></i>
                    <h6 class="mb-3">تقليل كمية المنتج في الطلب</h6>
                    <p class="text-muted mb-3">
                        <strong id="productNameToDecrease"></strong>
                    </p>
                </div>
                
                <!-- Current Quantity Display -->
                <div class="row mb-4">
                    <div class="col-6">
                        <div class="card bg-light">
                            <div class="card-body text-center py-3">
                                <h6 class="card-title mb-1">الكمية الحالية</h6>
                                <span class="badge bg-info fs-5" id="currentQuantityDisplay">0</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="card bg-light">
                            <div class="card-body text-center py-3">
                                <h6 class="card-title mb-1">سيتم استرداد</h6>
                                <span class="badge bg-success fs-5" id="quantityDifferenceDisplay">0</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <form id="decreaseQuantityForm" method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="newQuantity" class="form-label">الكمية الجديدة:</label>
                        <input type="number" class="form-control" id="newQuantity" name="new_quantity" 
                               min="1" required>
                        <div class="form-text">يجب أن تكون الكمية الجديدة أقل من الكمية الحالية وأكبر من صفر</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="decreaseReason" class="form-label">سبب تقليل الكمية:</label>
                        <select class="form-select" id="decreaseReason" name="reason" required>
                            <option value="توفر جزئي في المخزون">توفر جزئي في المخزون</option>
                            <option value="طلب تغيير من العميل">طلب تغيير من العميل</option>
                            <option value="تلف جزئي في المنتج">تلف جزئي في المنتج</option>
                            <option value="انتهاء صلاحية جزئية">انتهاء صلاحية جزئية</option>
                            <option value="تعديل الطلب">تعديل الطلب</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    
                    <div class="mb-3" id="customDecreaseReasonDiv" style="display: none;">
                        <label for="customDecreaseReason" class="form-label">السبب المخصص:</label>
                        <textarea class="form-control" id="customDecreaseReason" name="custom_reason" rows="2" placeholder="اكتب السبب هنا..."></textarea>
                    </div>
                    
                    <!-- Summary Section -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>
                            ملخص التغيير
                        </h6>
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">الكمية الحالية:</small><br>
                                <strong id="summaryCurrentQuantity">0</strong>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">الكمية الجديدة:</small><br>
                                <strong id="summaryNewQuantity">0</strong>
                            </div>
                        </div>
                        <hr class="my-2">
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">سيتم استرداد:</small><br>
                                <strong class="text-success" id="summaryQuantityDifference">0 قطعة</strong>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">توفير في السعر:</small><br>
                                <strong class="text-success" id="summaryPriceDifference">0 ج.م</strong>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-warning" onclick="confirmDecreaseQuantity()">
                    <i class="fas fa-minus-circle me-1"></i>
                    تأكيد تقليل الكمية
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Show/hide custom reason field based on selection
document.getElementById('decreaseReason').addEventListener('change', function() {
    const customReasonDiv = document.getElementById('customDecreaseReasonDiv');
    if (this.value === 'أخرى') {
        customReasonDiv.style.display = 'block';
        document.getElementById('customDecreaseReason').required = true;
    } else {
        customReasonDiv.style.display = 'none';
        document.getElementById('customDecreaseReason').required = false;
        document.getElementById('customDecreaseReason').value = '';
    }
});

// Update quantity difference and summary when new quantity changes
document.getElementById('newQuantity').addEventListener('input', function() {
    updateQuantitySummary();
});

function updateQuantitySummary() {
    const currentQuantity = parseInt(document.getElementById('currentQuantityDisplay').textContent) || 0;
    const newQuantity = parseInt(document.getElementById('newQuantity').value) || 0;
    const pricePerUnit = parseFloat(window.currentItemPricePerUnit) || 0;
    
    if (newQuantity > 0 && newQuantity < currentQuantity) {
        const quantityDifference = currentQuantity - newQuantity;
        const priceDifference = quantityDifference * pricePerUnit;
        
        document.getElementById('quantityDifferenceDisplay').textContent = quantityDifference;
        document.getElementById('summaryCurrentQuantity').textContent = currentQuantity;
        document.getElementById('summaryNewQuantity').textContent = newQuantity;
        document.getElementById('summaryQuantityDifference').textContent = quantityDifference + ' قطعة';
        document.getElementById('summaryPriceDifference').textContent = priceDifference.toFixed(2) + ' ج.م';
    } else {
        document.getElementById('quantityDifferenceDisplay').textContent = '0';
        document.getElementById('summaryNewQuantity').textContent = newQuantity || '0';
        document.getElementById('summaryQuantityDifference').textContent = '0 قطعة';
        document.getElementById('summaryPriceDifference').textContent = '0 ج.م';
    }
}
</script>
