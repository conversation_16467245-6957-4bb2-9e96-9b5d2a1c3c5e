<!-- Remove Item Confirmation Modal -->
<div class="modal fade" id="removeItemModal" tabindex="-1" aria-labelledby="removeItemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="removeItemModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكيد حذف المنتج
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <i class="fas fa-trash-alt text-danger fa-3x mb-3"></i>
                    <h6 class="mb-3">هل أنت متأكد من حذف هذا المنتج من الطلب؟</h6>
                    <p class="text-muted mb-3">
                        <strong id="productNameToRemove"></strong>
                    </p>
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم استرداد الكمية إلى المخزون وإعادة حساب إجمالي الطلب
                    </div>
                </div>
                
                <form id="removeItemForm" method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="removalReason" class="form-label">سبب الحذف:</label>
                        <select class="form-select" id="removalReason" name="reason" required>
                            <option value="المنتج غير متوفر في المخزون">المنتج غير متوفر في المخزون</option>
                            <option value="انتهت صلاحية المنتج">انتهت صلاحية المنتج</option>
                            <option value="تلف في المنتج">تلف في المنتج</option>
                            <option value="طلب من العميل">طلب من العميل</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    
                    <div class="mb-3" id="customReasonDiv" style="display: none;">
                        <label for="customReason" class="form-label">السبب المخصص:</label>
                        <textarea class="form-control" id="customReason" name="custom_reason" rows="2" placeholder="اكتب السبب هنا..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-danger" onclick="confirmRemoveItem()">
                    <i class="fas fa-trash-alt me-1"></i>
                    تأكيد الحذف
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Show/hide custom reason field based on selection
document.getElementById('removalReason').addEventListener('change', function() {
    const customReasonDiv = document.getElementById('customReasonDiv');
    if (this.value === 'أخرى') {
        customReasonDiv.style.display = 'block';
        document.getElementById('customReason').required = true;
    } else {
        customReasonDiv.style.display = 'none';
        document.getElementById('customReason').required = false;
        document.getElementById('customReason').value = '';
    }
});
</script>
