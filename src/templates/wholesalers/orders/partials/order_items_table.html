<div class="table-responsive">
    <table class="table table-hover mb-0">
        <thead>
            <tr>
                <th>المنتج</th>
                <th>الشركة</th>
                <th>السعر</th>
                <th>الكمية</th>
                <th>الإجمالي</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            {% for item in order_items %}
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        {% if item.product_item.product.image %}
                        <img src="{{ item.product_item.product.image.url }}"
                             alt="{{ item.product_item.product.name }}"
                             class="rounded me-3"
                             style="width: 50px; height: 50px; object-fit: cover;">
                        {% else %}
                        <div class="bg-light rounded d-flex align-items-center justify-content-center me-3"
                             style="width: 50px; height: 50px;">
                            <i class="fas fa-image text-muted"></i>
                        </div>
                        {% endif %}
                        <div>
                            <div class="fw-semibold">{{ item.product_item.product.name }}</div>
                            <small class="text-muted">{{ item.product_item.product.title }}</small>
                            {% if item.product_item.product.barcode %}
                            <small class="text-muted d-block">{{ item.product_item.product.barcode }}</small>
                            {% endif %}
                        </div>
                    </div>
                </td>
                <td>
                    {% if item.product_item.product.company %}
                    <span class="badge bg-light text-dark">{{ item.product_item.product.company.name }}</span>
                    {% else %}
                    <span class="text-muted">غير محدد</span>
                    {% endif %}
                </td>
                <td>
                    <span class="fw-semibold">{{ item.price_per_unit }} ج.م</span>
                </td>
                <td>
                    <span class="badge bg-info">{{ item.quantity }}</span>
                </td>
                <td>
                    <span class="fw-bold text-success">{{ item.total_price }} ج.م</span>
                </td>
                <td>
                    {% if order.status == 'pending' or order.status == 'processing' %}
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-outline-warning"
                                onclick="showDecreaseQuantityModal({{ order.id }}, {{ item.id }}, '{{ item.product_item.product.name|escapejs }}', {{ item.quantity }}, {{ item.price_per_unit }})"
                                title="تقليل الكمية">
                            <i class="fas fa-minus-circle me-1"></i>
                            تقليل
                        </button>
                        <button class="btn btn-sm btn-outline-danger"
                                onclick="showRemoveItemModal({{ order.id }}, {{ item.id }}, '{{ item.product_item.product.name|escapejs }}')"
                                title="حذف المنتج">
                            <i class="fas fa-trash-alt me-1"></i>
                            حذف
                        </button>
                    </div>
                    {% else %}
                    <span class="text-muted small">لا يمكن التعديل</span>
                    {% endif %}
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="text-center py-4">
                    <div class="text-muted">
                        <i class="fas fa-box-open fa-2x mb-2"></i>
                        <p class="mb-0">لا توجد منتجات في هذا الطلب</p>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot class="table-light">
            <tr>
                <th colspan="5" class="text-end">المجموع الإجمالي:</th>
                <th class="text-success fs-5">{{ order.products_total_price }} ج.م</th>
            </tr>
        </tfoot>
    </table>
</div>
