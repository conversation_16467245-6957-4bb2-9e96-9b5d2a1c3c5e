<tr data-order-id="{{ order.id }}" class="order-row" onclick="window.location.href='{% url 'order_detail' order.id %}'">
    <td>
        <div class="d-flex align-items-center">
            <strong class="text-primary">#{{ order.id }}</strong>
            {% if order.status == 'pending' %}
                <span class="badge bg-warning ms-2 pulse">جديد</span>
            {% endif %}
        </div>
    </td>
    
    <td>
        <div class="d-flex align-items-center">
            <div class="avatar-sm bg-light rounded-circle d-flex align-items-center justify-content-center me-2">
                <i class="fas fa-store text-muted"></i>
            </div>
            <div>
                <div class="fw-semibold">{{ order.store.name }}</div>
                <small class="text-muted">{{ order.store.owner.username }}</small>
                {% if order.store.city %}
                <small class="text-muted d-block">{{ order.store.city.name }}</small>
                {% endif %}
            </div>
        </div>
    </td>
    
    <td>
        <span class="badge bg-info">{{ order.products_total_quantity }} منتج</span>
    </td>
    
    <td>
        <div>
            <span class="fw-bold text-success fs-6">{{ order.total_price }} ج.م</span>
            {% if order.fees > 0 %}
            <small class="text-muted d-block">رسوم: {{ order.fees }} ج.م</small>
            {% endif %}
        </div>
    </td>
    
    <td>
        {% if order.status == 'pending' %}
            <span class="badge bg-warning">في الانتظار</span>
        {% elif order.status == 'processing' %}
            <span class="badge bg-info">قيد المعالجة</span>
        {% elif order.status == 'shipped' %}
            <span class="badge bg-primary">تم الشحن</span>
        {% elif order.status == 'delivered' %}
            <span class="badge bg-success">تم التسليم</span>
        {% elif order.status == 'cancelled' %}
            <span class="badge bg-danger">ملغي</span>
        {% endif %}
    </td>
    
    <td>
        <div>
            <span class="text-muted">{{ order.created_at|timesince }} مضت</span>
            <small class="text-muted d-block">{{ order.created_at|date:"Y/m/d H:i" }}</small>
        </div>
    </td>
    
    <td>
        <div class="btn-group btn-group-sm">
            <!-- View Details Button -->
            <a href="{% url 'order_detail' order.id %}" class="btn btn-outline-primary" title="عرض التفاصيل">
                <i class="fas fa-eye"></i>
            </a>
            
            <!-- Status Update Buttons -->
            {% if order.status == 'pending' %}
                <button class="btn btn-outline-success" 
                        onclick="updateOrderStatus({{ order.id }}, 'processing')" 
                        title="قبول الطلب">
                    <i class="fas fa-check"></i>
                </button>
                <button class="btn btn-outline-danger" 
                        onclick="updateOrderStatus({{ order.id }}, 'cancelled')" 
                        title="رفض الطلب">
                    <i class="fas fa-times"></i>
                </button>
            {% elif order.status == 'processing' %}
                <button class="btn btn-outline-primary" 
                        onclick="updateOrderStatus({{ order.id }}, 'shipped')" 
                        title="تم الشحن">
                    <i class="fas fa-shipping-fast"></i>
                </button>
            {% elif order.status == 'shipped' %}
                <button class="btn btn-outline-success" 
                        onclick="updateOrderStatus({{ order.id }}, 'delivered')" 
                        title="تم التسليم">
                    <i class="fas fa-check-circle"></i>
                </button>
            {% endif %}
            
            <!-- Print Button -->
            <button class="btn btn-outline-secondary" 
                    onclick="printOrder({{ order.id }})" 
                    title="طباعة">
                <i class="fas fa-print"></i>
            </button>
        </div>
    </td>
</tr>

<style>
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.order-row:hover {
    background-color: var(--light-green) !important;
}

.avatar-sm {
    width: 32px;
    height: 32px;
}
</style>
