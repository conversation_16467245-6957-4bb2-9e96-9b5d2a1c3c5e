{% extends 'wholesalers/base.html' %}

{% block title %}تعديل مجمع للمنتجات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-edit me-2 text-success"></i>
                    تعديل مجمع للمنتجات
                </h1>
                <p class="text-muted mb-0">تحديث معلومات عدة منتجات في نفس الوقت</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{% url 'items_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للمخزون
                </a>
            </div>
        </div>
    </div>
</div>

{% if items %}
<!-- Bulk Update Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-cogs me-2"></i>
            إعدادات التحديث المجمع
        </h5>
    </div>
    <div class="card-body">
        <form method="post" id="bulkEditForm">
            {% csrf_token %}
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة:</strong> اترك الحقول فارغة إذا كنت لا تريد تحديثها. سيتم تطبيق القيم المدخلة على جميع المنتجات المحددة.
            </div>
            
            <div class="row g-3 mb-4">
                <div class="col-md-3">
                    <label for="update_price" class="form-label">
                        <i class="fas fa-tag me-1"></i>
                        السعر الجديد
                    </label>
                    <div class="input-group">
                        <input type="number" 
                               class="form-control" 
                               id="update_price" 
                               name="update_price" 
                               step="0.01" 
                               min="0"
                               placeholder="اتركه فارغاً لعدم التحديث">
                        <span class="input-group-text">ج.م</span>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <label for="update_inventory" class="form-label">
                        <i class="fas fa-warehouse me-1"></i>
                        الكمية الجديدة
                    </label>
                    <input type="number" 
                           class="form-control" 
                           id="update_inventory" 
                           name="update_inventory" 
                           min="0"
                           placeholder="اتركه فارغاً لعدم التحديث">
                </div>
                
                <div class="col-md-3">
                    <label for="update_min_order" class="form-label">
                        <i class="fas fa-arrow-down me-1"></i>
                        الحد الأدنى للطلب
                    </label>
                    <input type="number" 
                           class="form-control" 
                           id="update_min_order" 
                           name="update_min_order" 
                           min="1"
                           placeholder="اتركه فارغاً لعدم التحديث">
                </div>
                
                <div class="col-md-3">
                    <label for="update_max_order" class="form-label">
                        <i class="fas fa-arrow-up me-1"></i>
                        الحد الأقصى للطلب
                    </label>
                    <input type="number" 
                           class="form-control" 
                           id="update_max_order" 
                           name="update_max_order" 
                           min="1"
                           placeholder="اتركه فارغاً لعدم التحديث">
                </div>
            </div>
            
            <div class="d-flex justify-content-between align-items-center">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                    <label class="form-check-label" for="selectAll">
                        تحديد جميع المنتجات
                    </label>
                </div>
                
                <button type="submit" class="btn btn-success" id="updateBtn">
                    <i class="fas fa-save me-2"></i>
                    تطبيق التحديثات
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Items List -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            المنتجات المحددة ({{ items.count }})
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th width="40">
                            <input type="checkbox" id="selectAllTable" onchange="toggleSelectAllTable()" class="form-check-input">
                        </th>
                        <th>المنتج</th>
                        <th>السعر الحالي</th>
                        <th>المخزون الحالي</th>
                        <th>الحد الأدنى</th>
                        <th>الحد الأقصى</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in items %}
                    <tr>
                        <td>
                            <input type="checkbox" 
                                   name="selected_items" 
                                   value="{{ item.id }}" 
                                   class="form-check-input item-checkbox"
                                   form="bulkEditForm"
                                   checked>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                {% if item.product.image %}
                                <img src="{{ item.product.image.url }}" 
                                     alt="{{ item.product.name }}" 
                                     class="rounded me-3" 
                                     style="width: 50px; height: 50px; object-fit: cover;">
                                {% else %}
                                <div class="bg-light rounded d-flex align-items-center justify-content-center me-3" 
                                     style="width: 50px; height: 50px;">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                                {% endif %}
                                <div>
                                    <div class="fw-semibold">{{ item.product.name }}</div>
                                    <small class="text-muted">{{ item.product.title }}</small>
                                    {% if item.product.company %}
                                    <small class="text-muted d-block">{{ item.product.company.name }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="fw-bold text-success">{{ item.base_price }} ج.م</span>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <span class="me-2">{{ item.inventory_count }}</span>
                                {% if item.inventory_count == 0 %}
                                    <span class="badge bg-danger">نفد</span>
                                {% elif item.inventory_count < 10 %}
                                    <span class="badge bg-warning">منخفض</span>
                                {% else %}
                                    <span class="badge bg-success">متوفر</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>{{ item.minimum_order_quantity }}</td>
                        <td>{{ item.maximum_order_quantity|default:"غير محدد" }}</td>
                        <td>
                            {% if item.base_price == 0 %}
                                <span class="badge bg-warning">يحتاج تسعير</span>
                            {% else %}
                                <span class="badge bg-success">جاهز</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

{% else %}
<div class="card">
    <div class="card-body text-center py-5">
        <i class="fas fa-boxes fa-4x text-muted mb-3"></i>
        <h4 class="text-muted">لا توجد منتجات للتعديل</h4>
        <p class="text-muted">لم يتم تحديد أي منتجات للتعديل المجمع</p>
        <div class="d-flex gap-2 justify-content-center">
            <a href="{% url 'items_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للمخزون
            </a>
            <a href="{% url 'bulk_add_items' %}" class="btn btn-success">
                <i class="fas fa-upload me-2"></i>
                إضافة مجمعة
            </a>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const selectAllTableCheckbox = document.getElementById('selectAllTable');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    
    itemCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    
    selectAllTableCheckbox.checked = selectAllCheckbox.checked;
    updateSubmitButton();
}

function toggleSelectAllTable() {
    const selectAllTableCheckbox = document.getElementById('selectAllTable');
    const selectAllCheckbox = document.getElementById('selectAll');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    
    itemCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllTableCheckbox.checked;
    });
    
    selectAllCheckbox.checked = selectAllTableCheckbox.checked;
    updateSubmitButton();
}

function updateSubmitButton() {
    const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
    const updateBtn = document.getElementById('updateBtn');
    
    if (checkedBoxes.length > 0) {
        updateBtn.textContent = `تطبيق التحديثات (${checkedBoxes.length})`;
        updateBtn.disabled = false;
    } else {
        updateBtn.textContent = 'تطبيق التحديثات';
        updateBtn.disabled = true;
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to individual checkboxes
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSubmitButton();
            
            // Update select all checkboxes
            const allChecked = Array.from(itemCheckboxes).every(cb => cb.checked);
            const noneChecked = Array.from(itemCheckboxes).every(cb => !cb.checked);
            
            document.getElementById('selectAll').checked = allChecked;
            document.getElementById('selectAllTable').checked = allChecked;
            
            if (!allChecked && !noneChecked) {
                document.getElementById('selectAll').indeterminate = true;
                document.getElementById('selectAllTable').indeterminate = true;
            } else {
                document.getElementById('selectAll').indeterminate = false;
                document.getElementById('selectAllTable').indeterminate = false;
            }
        });
    });
    
    // Initial update
    updateSubmitButton();
    
    // Form validation
    const form = document.getElementById('bulkEditForm');
    form.addEventListener('submit', function(e) {
        const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
        
        if (checkedBoxes.length === 0) {
            e.preventDefault();
            alert('يرجى اختيار منتج واحد على الأقل');
            return;
        }
        
        // Check if at least one update field is filled
        const updatePrice = document.getElementById('update_price').value;
        const updateInventory = document.getElementById('update_inventory').value;
        const updateMinOrder = document.getElementById('update_min_order').value;
        const updateMaxOrder = document.getElementById('update_max_order').value;
        
        if (!updatePrice && !updateInventory && !updateMinOrder && !updateMaxOrder) {
            e.preventDefault();
            alert('يرجى إدخال قيمة واحدة على الأقل للتحديث');
            return;
        }
        
        // Confirm update
        if (!confirm(`هل أنت متأكد من تحديث ${checkedBoxes.length} منتج؟`)) {
            e.preventDefault();
            return;
        }
        
        // Show loading state
        const updateBtn = document.getElementById('updateBtn');
        updateBtn.disabled = true;
        updateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...';
    });
});
</script>
{% endblock %}
