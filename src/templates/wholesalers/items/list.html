{% extends 'wholesalers/base.html' %}

{% block title %}إدارة المنتجات والمخزون{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="fas fa-boxes me-2 text-success"></i>
                إدارة المنتجات والمخزون
            </h1>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-success" onclick="refreshItems()">
                    <i class="fas fa-sync-alt me-2"></i>
                    تحديث
                </button>
                <button class="btn btn-success" onclick="location.href='{% url 'bulk_add_items' %}'">
                    <i class="fas fa-upload me-2"></i>
                    إضافة مجمعة
                </button>
                <button class="btn btn-primary" onclick="location.href='{% url 'item_create_step1' %}'">
                    <i class="fas fa-plus me-2"></i>
                    إضافة منتج
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card stats-card items">
            <div class="card-body text-center">
                <div class="icon">
                    <i class="fas fa-boxes"></i>
                </div>
                <h3 class="mb-1">{{ total_items }}</h3>
                <p class="text-muted mb-0">إجمالي المنتجات</p>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card stats-card alerts">
            <div class="card-body text-center">
                <div class="icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3 class="mb-1">{{ low_stock_items }}</h3>
                <p class="text-muted mb-0">مخزون منخفض</p>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card stats-card sales">
            <div class="card-body text-center">
                <div class="icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <h3 class="mb-1">{{ out_of_stock_items }}</h3>
                <p class="text-muted mb-0">نفد المخزون</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" id="filterForm" hx-get="{% url 'items_list' %}" hx-target="#itemsContainer" hx-trigger="change, submit">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           placeholder="اسم المنتج أو الباركود" value="{{ search_query }}">
                </div>
                
                <div class="col-md-2">
                    <label for="company" class="form-label">الشركة</label>
                    <select class="form-select" id="company" name="company">
                        <option value="">جميع الشركات</option>
                        {% for company in companies %}
                        <option value="{{ company.id }}" {% if company_filter == company.id|stringformat:"s" %}selected{% endif %}>
                            {{ company.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="category" class="form-label">الفئة</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">جميع الفئات</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if category_filter == category.id|stringformat:"s" %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="stock" class="form-label">حالة المخزون</label>
                    <select class="form-select" id="stock" name="stock">
                        <option value="">جميع الحالات</option>
                        <option value="available" {% if stock_filter == 'available' %}selected{% endif %}>متوفر</option>
                        <option value="low" {% if stock_filter == 'low' %}selected{% endif %}>منخفض</option>
                        <option value="out" {% if stock_filter == 'out' %}selected{% endif %}>نفد</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="sort" class="form-label">ترتيب حسب</label>
                    <select class="form-select" id="sort" name="sort">
                        <option value="-created_at" {% if sort_filter == '-created_at' %}selected{% endif %}>الأحدث</option>
                        <option value="product__name" {% if sort_filter == 'product__name' %}selected{% endif %}>الاسم (أ-ي)</option>
                        <option value="-product__name" {% if sort_filter == '-product__name' %}selected{% endif %}>الاسم (ي-أ)</option>
                        <option value="inventory_count" {% if sort_filter == 'inventory_count' %}selected{% endif %}>المخزون (أقل)</option>
                        <option value="-inventory_count" {% if sort_filter == '-inventory_count' %}selected{% endif %}>المخزون (أكثر)</option>
                        <option value="base_price" {% if sort_filter == 'base_price' %}selected{% endif %}>السعر (أقل)</option>
                        <option value="-base_price" {% if sort_filter == '-base_price' %}selected{% endif %}>السعر (أكثر)</option>
                    </select>
                </div>
                
                <div class="col-md-1 d-flex align-items-end">
                    <button type="button" class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Items List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة المنتجات
        </h5>
        <div class="d-flex gap-2">
            <button class="btn btn-sm btn-outline-primary" onclick="toggleView()">
                <i class="fas fa-th-large me-1"></i>
                عرض البطاقات
            </button>
            <button class="btn btn-sm btn-outline-success" onclick="bulkEdit()">
                <i class="fas fa-edit me-1"></i>
                تعديل مجمع
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <div id="itemsContainer">
            {% include 'wholesalers/items/partials/items_list.html' %}
        </div>
    </div>
</div>

<!-- Bulk Edit Modal -->
<div class="modal fade" id="bulkEditModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل مجمع للمنتجات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="bulkEditForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="bulkPrice" class="form-label">تحديث السعر</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="bulkPrice" step="0.01" placeholder="السعر الجديد">
                                <span class="input-group-text">ج.م</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="bulkInventory" class="form-label">تحديث المخزون</label>
                            <input type="number" class="form-control" id="bulkInventory" placeholder="الكمية الجديدة">
                        </div>
                        <div class="col-md-6">
                            <label for="bulkMinOrder" class="form-label">الحد الأدنى للطلب</label>
                            <input type="number" class="form-control" id="bulkMinOrder" placeholder="الحد الأدنى">
                        </div>
                        <div class="col-md-6">
                            <label for="bulkMaxOrder" class="form-label">الحد الأقصى للطلب</label>
                            <input type="number" class="form-control" id="bulkMaxOrder" placeholder="الحد الأقصى">
                        </div>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">سيتم تطبيق التحديثات على المنتجات المحددة فقط</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="applyBulkEdit()">تطبيق التحديثات</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedItems = new Set();
let viewMode = 'table'; // 'table' or 'cards'

function refreshItems() {
    htmx.trigger('#filterForm', 'submit');
}

function clearFilters() {
    document.getElementById('search').value = '';
    document.getElementById('company').value = '';
    document.getElementById('category').value = '';
    document.getElementById('stock').value = '';
    document.getElementById('sort').value = '-created_at';
    htmx.trigger('#filterForm', 'submit');
}

function toggleView() {
    viewMode = viewMode === 'table' ? 'cards' : 'table';
    // Implementation for switching between table and card view
    // This would require additional templates and logic
}

function selectItem(itemId) {
    const checkbox = document.querySelector(`input[data-item-id="${itemId}"]`);
    if (checkbox.checked) {
        selectedItems.add(itemId);
    } else {
        selectedItems.delete(itemId);
    }
    updateBulkEditButton();
}

function selectAllItems() {
    const checkboxes = document.querySelectorAll('input[data-item-id]');
    const selectAllCheckbox = document.getElementById('selectAll');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
        const itemId = checkbox.getAttribute('data-item-id');
        if (selectAllCheckbox.checked) {
            selectedItems.add(itemId);
        } else {
            selectedItems.delete(itemId);
        }
    });
    updateBulkEditButton();
}

function updateBulkEditButton() {
    const bulkEditBtn = document.querySelector('[onclick="bulkEdit()"]');
    if (selectedItems.size > 0) {
        bulkEditBtn.textContent = `تعديل مجمع (${selectedItems.size})`;
        bulkEditBtn.classList.remove('btn-outline-success');
        bulkEditBtn.classList.add('btn-success');
    } else {
        bulkEditBtn.innerHTML = '<i class="fas fa-edit me-1"></i> تعديل مجمع';
        bulkEditBtn.classList.remove('btn-success');
        bulkEditBtn.classList.add('btn-outline-success');
    }
}

function bulkEdit() {
    if (selectedItems.size === 0) {
        alert('يرجى تحديد منتج واحد على الأقل');
        return;
    }
    
    const modal = new bootstrap.Modal(document.getElementById('bulkEditModal'));
    modal.show();
}

function applyBulkEdit() {
    const formData = new FormData();
    formData.append('items', Array.from(selectedItems).join(','));
    formData.append('price', document.getElementById('bulkPrice').value);
    formData.append('inventory', document.getElementById('bulkInventory').value);
    formData.append('min_order', document.getElementById('bulkMinOrder').value);
    formData.append('max_order', document.getElementById('bulkMaxOrder').value);
    formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');
    
    fetch('/wholesaler/items/bulk-edit/', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ أثناء التحديث');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء التحديث');
    });
}

function quickEdit(itemId, field, value) {
    const formData = new FormData();
    formData.append(field, value);
    formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');
    
    fetch(`/wholesaler/items/${itemId}/edit/`, {
        method: 'POST',
        body: formData,
        headers: {
            'HX-Request': 'true'
        }
    })
    .then(async response => {
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            const data = await response.json();
            if (!data.success) {
                alert(data.error || 'حدث خطأ أثناء التحديث');
                return;
            }
        } else {
            const html = await response.text();
            const itemRow = document.querySelector(`[data-item-id="${itemId}"]`).closest('tr');
            if (itemRow) {
                itemRow.outerHTML = html;
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء التحديث');
    });
}

// Auto-save on input change with debounce
let debounceTimers = {};

function debounceQuickEdit(itemId, field, value) {
    clearTimeout(debounceTimers[`${itemId}-${field}`]);
    debounceTimers[`${itemId}-${field}`] = setTimeout(() => {
        quickEdit(itemId, field, value);
    }, 1000);
}
</script>
{% endblock %}
