{% if products %}
<div class="row g-3 p-3">
    {% for product in products %}
    <div class="col-lg-3 col-md-4 col-sm-6">
        <div class="card product-card h-100" onclick="selectProduct({{ product.id }}, '{{ product.name|escapejs }}', '{% if product.image %}{{ product.image.url }}{% endif %}', '{% if product.company %}{{ product.company.name|escapejs }}{% endif %}', '{% if product.category %}{{ product.category.name|escapejs }}{% endif %}')">
            <div class="card-body p-3">
                <!-- Product Image -->
                <div class="text-center mb-3">
                    {% if product.image %}
                    <img src="{{ product.image.url }}" 
                         alt="{{ product.name }}" 
                         class="product-image">
                    {% else %}
                    <div class="product-placeholder">
                        <i class="fas fa-image fa-3x"></i>
                    </div>
                    {% endif %}
                </div>
                
                <!-- Product Info -->
                <div class="text-center">
                    <h6 class="card-title mb-2">{{ product.name }}</h6>
                    <p class="card-text text-muted small mb-2">{{ product.title|truncatechars:50 }}</p>
                    
                    <!-- Company and Category -->
                    <div class="mb-2">
                        {% if product.company %}
                        <span class="badge bg-light text-dark me-1">{{ product.company.name }}</span>
                        {% endif %}
                        {% if product.category %}
                        <span class="badge bg-secondary">{{ product.category.name }}</span>
                        {% endif %}
                    </div>
                    
                    <!-- Barcode -->
                    {% if product.barcode %}
                    <small class="text-muted d-block mb-2">{{ product.barcode }}</small>
                    {% endif %}
                    
                    <!-- Select Button -->
                    <button type="button" class="btn btn-outline-primary btn-sm w-100">
                        <i class="fas fa-plus me-1"></i>
                        اختيار هذا المنتج
                    </button>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if page_obj.has_other_pages %}
<div class="d-flex justify-content-between align-items-center p-3 border-top">
    <div class="text-muted">
        عرض {{ page_obj.start_index }} - {{ page_obj.end_index }} من {{ page_obj.paginator.count }} منتج
    </div>
    
    <nav aria-label="تنقل الصفحات">
        <ul class="pagination pagination-sm mb-0">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" 
                       hx-get="{% url 'item_create_step2' %}?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" 
                       hx-target="#productsContainer">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            {% endif %}
            
            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}"
                           hx-get="{% url 'item_create_step2' %}?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}" 
                           hx-target="#productsContainer">
                            {{ num }}
                        </a>
                    </li>
                {% endif %}
            {% endfor %}
            
            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}"
                       hx-get="{% url 'item_create_step2' %}?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" 
                       hx-target="#productsContainer">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
</div>
{% endif %}

{% else %}
<div class="text-center py-5">
    {% if search_query %}
    <i class="fas fa-search fa-4x text-muted mb-3"></i>
    <h4 class="text-muted">لا توجد نتائج</h4>
    <p class="text-muted">لم يتم العثور على منتجات تطابق "{{ search_query }}"</p>
    <button class="btn btn-outline-primary" onclick="document.getElementById('search').value=''; document.getElementById('searchForm').submit();">
        <i class="fas fa-times me-2"></i>
        مسح البحث
    </button>
    {% else %}
    <i class="fas fa-boxes fa-4x text-muted mb-3"></i>
    <h4 class="text-muted">لا توجد منتجات متاحة</h4>
    <p class="text-muted">جميع المنتجات موجودة بالفعل في مخزونك أو لا توجد منتجات في النظام</p>
    <a href="{% url 'items_list' %}" class="btn btn-outline-primary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة للمخزون
    </a>
    {% endif %}
</div>
{% endif %}
