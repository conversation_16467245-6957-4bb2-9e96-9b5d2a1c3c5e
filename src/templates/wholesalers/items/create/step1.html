{% extends 'wholesalers/base.html' %}

{% block title %}إضافة منتج جديد - اختيار الشركة{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Progress Steps -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="step-item active">
                            <div class="step-number">1</div>
                            <div class="step-title">اختيار الشركة</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-title">اختيار المنتج</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-title">تفاصيل المنتج</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>
                    اختيار الشركة (اختياري)
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    يمكنك اختيار شركة معينة لتصفية المنتجات في الخطوة التالية، أو تخطي هذه الخطوة لعرض جميع المنتجات.
                </div>

                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-4">
                        <label for="company_id" class="form-label">الشركة</label>
                        <select class="form-select" id="company_id" name="company_id">
                            <option value="">جميع الشركات</option>
                            {% for company in companies %}
                            <option value="{{ company.id }}">{{ company.name }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">اختر شركة معينة أو اتركها فارغة لعرض جميع المنتجات</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'items_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            التالي
                            <i class="fas fa-arrow-left ms-2"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Company Statistics -->
        {% if companies %}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات الشركات
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for company in companies|slice:":6" %}
                    <div class="col-md-4 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="avatar-sm bg-light rounded-circle d-flex align-items-center justify-content-center">
                                    <i class="fas fa-building text-muted"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0">{{ company.name }}</h6>
                                <small class="text-muted">{{ company.title }}</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% if companies.count > 6 %}
                <div class="text-center">
                    <small class="text-muted">و {{ companies.count|add:"-6" }} شركة أخرى</small>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.step-item {
    position: relative;
    padding: 1rem 0;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin: 0 auto 0.5rem;
    transition: all 0.3s ease;
}

.step-item.active .step-number {
    background-color: var(--primary-green);
    color: white;
}

.step-item.completed .step-number {
    background-color: var(--secondary-green);
    color: white;
}

.step-title {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.step-item.active .step-title {
    color: var(--primary-green);
    font-weight: 600;
}

.step-item.completed .step-title {
    color: var(--secondary-green);
    font-weight: 600;
}

/* Connect steps with lines */
.step-item:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    right: -50%;
    width: 100%;
    height: 2px;
    background-color: #e9ecef;
    z-index: -1;
}

.step-item.completed:not(:last-child)::after {
    background-color: var(--secondary-green);
}

.avatar-sm {
    width: 32px;
    height: 32px;
}

@media (max-width: 768px) {
    .step-item:not(:last-child)::after {
        display: none;
    }
    
    .step-number {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }
    
    .step-title {
        font-size: 0.8rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on company select
    document.getElementById('company_id').focus();
    
    // Add keyboard shortcut for next step
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && e.ctrlKey) {
            document.querySelector('form').submit();
        }
    });
});
</script>
{% endblock %}
