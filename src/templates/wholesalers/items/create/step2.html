{% extends 'wholesalers/base.html' %}

{% block title %}إضافة منتج جديد - اختيار المنتج{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Progress Steps -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="step-item completed">
                            <div class="step-number">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-title">اختيار الشركة</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="step-item active">
                            <div class="step-number">2</div>
                            <div class="step-title">اختيار المنتج</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-title">تفاصيل المنتج</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-search me-2"></i>
                    البحث عن المنتجات
                </h5>
            </div>
            <div class="card-body">
                <form method="get" id="searchForm" hx-get="{% url 'item_create_step2' %}" hx-target="#productsContainer" hx-trigger="input delay:500ms, submit">
                    <div class="row g-3">
                        <div class="col-md-8">
                            <input type="text" 
                                   class="form-control" 
                                   id="search" 
                                   name="search" 
                                   placeholder="ابحث بالاسم أو الباركود أو الوصف..."
                                   value="{{ search_query }}"
                                   autocomplete="off">
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                        </div>
                    </div>
                </form>
                
                {% if selected_company %}
                <div class="mt-3">
                    <span class="badge bg-info">
                        <i class="fas fa-filter me-1"></i>
                        مفلتر حسب: {{ selected_company.name }}
                    </span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Products Grid -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-boxes me-2"></i>
                    اختر المنتج
                </h5>
                <div class="d-flex gap-2">
                    <a href="{% url 'item_create_step1' %}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-right me-1"></i>
                        السابق
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="productsContainer">
                    {% include 'wholesalers/items/create/partials/products_list.html' %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Product Selection Modal -->
<div class="modal fade" id="productModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد اختيار المنتج</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Product details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmSelection">
                    تأكيد الاختيار
                    <i class="fas fa-arrow-left ms-2"></i>
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.step-item {
    position: relative;
    padding: 1rem 0;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin: 0 auto 0.5rem;
    transition: all 0.3s ease;
}

.step-item.active .step-number {
    background-color: var(--primary-green);
    color: white;
}

.step-item.completed .step-number {
    background-color: var(--secondary-green);
    color: white;
}

.step-title {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.step-item.active .step-title {
    color: var(--primary-green);
    font-weight: 600;
}

.step-item.completed .step-title {
    color: var(--secondary-green);
    font-weight: 600;
}

/* Connect steps with lines */
.step-item:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    right: -50%;
    width: 100%;
    height: 2px;
    background-color: #e9ecef;
    z-index: -1;
}

.step-item.completed:not(:last-child)::after {
    background-color: var(--secondary-green);
}

.product-card {
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.product-card:hover {
    border-color: var(--primary-green);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.15);
}

.product-card.selected {
    border-color: var(--primary-green);
    background-color: var(--light-green);
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 0.5rem;
}

.product-placeholder {
    width: 100%;
    height: 200px;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

@media (max-width: 768px) {
    .step-item:not(:last-child)::after {
        display: none;
    }
    
    .step-number {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }
    
    .step-title {
        font-size: 0.8rem;
    }
    
    .product-image,
    .product-placeholder {
        height: 150px;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let selectedProductId = null;

function selectProduct(productId, productName, productImage, productCompany, productCategory) {
    selectedProductId = productId;
    
    // Update modal content
    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-4">
                ${productImage ? 
                    `<img src="${productImage}" alt="${productName}" class="img-fluid rounded">` :
                    `<div class="product-placeholder">
                        <i class="fas fa-image fa-3x"></i>
                    </div>`
                }
            </div>
            <div class="col-md-8">
                <h5>${productName}</h5>
                <p class="text-muted mb-2">${productCompany || 'غير محدد'}</p>
                <span class="badge bg-secondary">${productCategory || 'غير محدد'}</span>
                <div class="mt-3">
                    <p class="mb-0">هل أنت متأكد من اختيار هذا المنتج؟</p>
                    <small class="text-muted">ستتمكن من تحديد السعر والكمية في الخطوة التالية.</small>
                </div>
            </div>
        </div>
    `;
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('productModal'));
    modal.show();
}

document.getElementById('confirmSelection').addEventListener('click', function() {
    if (selectedProductId) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">
            <input type="hidden" name="product_id" value="${selectedProductId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
});

// Auto-focus on search input
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('search').focus();
    
    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const modal = bootstrap.Modal.getInstance(document.getElementById('productModal'));
            if (modal) {
                modal.hide();
            }
        }
    });
});
</script>
{% endblock %}
