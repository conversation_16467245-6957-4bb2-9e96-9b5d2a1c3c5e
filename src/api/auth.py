from ninja import Schema
from ninja.errors import HttpError
from typing import Optional
from accounts.models import CustomUser
import logging
from .utils import generate_jwt_token
from datetime import datetime

logger = logging.getLogger(__name__)


class LoginResponse(Schema):
    success: bool
    token: str
    user_id: int
    phone: str
    is_phone_verified: bool
    wholesaler_id: Optional[int] = None


class LoginRequest(Schema):
    phone: str
    password: str


class RegisterRequest(Schema):
    name: str
    password: str
    phone: str
    email: Optional[str] = None


class RegisterResponse(Schema):
    success: bool
    user_id: int
    phone: str
    message: str
    token: str


class UserResponse(Schema):
    """Response schema for user data"""

    id: int
    username: str
    email: Optional[str] = None
    phone: str
    phone_verified: bool
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: bool
    date_joined: datetime
    wholesaler_id: Optional[int] = None


class UserUpdateRequest(Schema):
    """Request schema for updating user data"""

    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None


# POST: /api/v2/login
def login(request, data: LoginRequest) -> LoginResponse:
    """
    Simple login with phone and password
    Returns JWT token and user details including wholesaler_id if applicable
    """
    try:
        # Find user by phone
        user = CustomUser.objects.filter(phone=data.phone).first()

        if not user or not user.check_password(data.password):
            raise HttpError(401, "Invalid phone number or password")

        if not user.is_active:
            raise HttpError(401, "Account is disabled")

        # Get wholesaler ID if user has one
        try:
            wholesaler = user.wholesalers.filter(deleted_at__isnull=True).first()
            wholesaler_id = wholesaler.id if wholesaler else None
        except Exception as e:
            logger.warning(f"Error checking wholesaler for user {user.id}: {str(e)}")
            wholesaler_id = None

        # Generate JWT token
        token = generate_jwt_token(user)

        return LoginResponse(
            success=True,
            token=token,
            user_id=user.id,
            phone=user.phone,
            is_phone_verified=user.phone_verified,
            wholesaler_id=wholesaler_id,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Login error: {str(e)}")
        raise HttpError(500, "Internal server error")


# POST: /api/v2/register
def register(request, data: RegisterRequest) -> RegisterResponse:
    """
    Simple user registration
    Creates user account with phone verification set to False
    """
    try:
        # Check if user already exists
        if CustomUser.objects.filter(phone=data.phone).exists():
            raise HttpError(400, "User with this phone number already exists")

        if CustomUser.objects.filter(username=data.name).exists():
            raise HttpError(400, "Username already taken")

        # Create new user
        user = CustomUser.objects.create_user(
            username=data.name,
            email=data.email or "",
            phone=data.phone,
            password=data.password,
            phone_verified=False,  # Phone verification required separately
        )

        token = generate_jwt_token(user)

        return RegisterResponse(
            success=True,
            user_id=user.id,
            phone=user.phone,
            message="User registered successfully. Phone verification required.",
            token=token,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Registration error: {str(e)}")
        raise HttpError(500, "Internal server error")


# GET: /api/v2/me
def get_current_user(request) -> UserResponse:
    """
    Get the current authenticated user's data
    Requires authentication via JWT token
    """
    try:
        # Get user from request (set by auth middleware)
        user = request.user

        if not user or not hasattr(user, "id"):
            raise HttpError(401, "Authentication required")

        # Get wholesaler ID if user has one
        try:
            wholesaler = user.wholesalers.filter(deleted_at__isnull=True).first()
            wholesaler_id = wholesaler.id if wholesaler else None
        except Exception as e:
            logger.warning(f"Error checking wholesaler for user {user.id}: {str(e)}")
            wholesaler_id = None

        return UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            phone=user.phone,
            phone_verified=user.phone_verified,
            first_name=user.first_name,
            last_name=user.last_name,
            is_active=user.is_active,
            date_joined=user.date_joined,
            wholesaler_id=wholesaler_id,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Get user error: {str(e)}")
        raise HttpError(500, "Internal server error")


# PUT: /api/v2/me
def update_current_user(request, data: UserUpdateRequest) -> UserResponse:
    """
    Update the current authenticated user's data
    Requires authentication via JWT token

    Fields that can be updated:
    - first_name
    - last_name
    - email
    - phone (requires verification if changed)
    """
    try:
        # Get user from request (set by auth middleware)
        user = request.user

        if not user or not hasattr(user, "id"):
            raise HttpError(401, "Authentication required")

        # Update fields if provided
        if data.first_name is not None:
            user.first_name = data.first_name

        if data.last_name is not None:
            user.last_name = data.last_name

        if data.email is not None:
            user.email = data.email

        # Handle phone number update - requires special handling
        if data.phone is not None and data.phone != user.phone:
            # Check if phone number is already in use
            if CustomUser.objects.filter(phone=data.phone).exists():
                raise HttpError(400, "Phone number already in use")

            # Update phone and mark as unverified
            user.phone = data.phone
            user.phone_verified = False

        # Save the updated user
        user.save()

        # Get wholesaler ID if user has one
        try:
            wholesaler = user.wholesalers.filter(deleted_at__isnull=True).first()
            wholesaler_id = wholesaler.id if wholesaler else None
        except Exception as e:
            logger.warning(f"Error checking wholesaler for user {user.id}: {str(e)}")
            wholesaler_id = None

        # Return the updated user data
        return UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            phone=user.phone,
            phone_verified=user.phone_verified,
            first_name=user.first_name,
            last_name=user.last_name,
            is_active=user.is_active,
            date_joined=user.date_joined,
            wholesaler_id=wholesaler_id,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Update user error: {str(e)}")
        raise HttpError(500, "Internal server error")
