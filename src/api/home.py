from decimal import Decimal
from typing import List, Optional
from ninja import Schema
from ninja.errors import HttpError
from django.core.paginator import Paginator
from django.core.cache import cache
from django.db.models import Q
from django.conf import settings
import logging

from products.schemas import CompanyOut, CategoryOut
from products.models import Product
from products.views import _product_to_schema
from products.home_views import _get_region_hierarchy
from products.utils import search_products_trigram

logger = logging.getLogger(__name__)


class ProductWithPricing(Schema):
    id: int
    name: str
    title: str
    barcode: str
    slug: str
    description: str
    image_url: Optional[str] = None
    company_id: Optional[int] = None
    category_id: Optional[int] = None
    company: Optional[CompanyOut] = None
    category: Optional[CategoryOut] = None
    unit: str
    unit_count: Decimal
    base_price: Optional[Decimal] = None  # lowest price or the wholesaler price
    other_price: Optional[Decimal] = None  # other prices or the highest price


class PaginatedProductResponse(Schema):
    """Response model for paginated products"""

    products: List[ProductWithPricing]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


def _get_filtered_products(
    region_id: Optional[int] = None,
    wholesaler_id: Optional[int] = None,
    search: Optional[str] = None,
    category_id: Optional[int] = None,
    company_id: Optional[int] = None,
):
    """
    Get filtered products based on various criteria.

    Args:
        region_id: Filter by region (includes parent regions)
        wholesaler_id: Filter by specific wholesaler
        search: Search in product name, title, description
        category_id: Filter by category
        company_id: Filter by company
    """
    # Start with base queryset
    queryset = Product.objects.filter(deleted_at__isnull=True)

    # Apply basic filters
    if category_id is not None:
        queryset = queryset.filter(
            category_id=category_id, category__deleted_at__isnull=True
        )

    if company_id is not None:
        queryset = queryset.filter(
            company_id=company_id, company__deleted_at__isnull=True
        )

    # Apply search filter
    if search:
        queryset = search_products_trigram(queryset, search)

    # Filter by products that have active wholesaler items
    queryset = queryset.filter(
        wholesalers__deleted_at__isnull=True,
        wholesalers__inventory_count__gt=0,
    )

    # Apply wholesaler filter
    if wholesaler_id is not None:
        queryset = queryset.filter(
            wholesalers__wholesaler_id=wholesaler_id,
            wholesalers__wholesaler__deleted_at__isnull=True,
        )

    # Apply region filter
    if region_id is not None:
        region_ids = _get_region_hierarchy(region_id)
        if region_ids:
            queryset = queryset.filter(
                wholesalers__wholesaler__region_min_charge__region_id__in=region_ids,
                wholesalers__wholesaler__region_min_charge__deleted_at__isnull=True,
            )

    return queryset.distinct().order_by("-created_at")


def _get_product_pricing_by_region(
    product_data: dict,
    region_id: Optional[int] = None,
    wholesaler_id: Optional[int] = None,
) -> dict:
    """
    Get pricing information for a product from wholesalers in the specified region.

    Args:
        product_data: Product dictionary data
        region_id: Optional region ID to filter wholesalers by region
        wholesaler_id: Optional specific wholesaler ID

    Returns:
        Dictionary with pricing information including lowest_price, price_range, etc.
    """
    from wholesalers.models import Item
    from wholesalers.schemas import WholesalerOut as WholesalerOutSchema

    # Start with base queryset for items of this product
    items_queryset = Item.objects.filter(
        product_id=product_data["id"],
        deleted_at__isnull=True,
        inventory_count__gt=0,
        wholesaler__deleted_at__isnull=True,
    ).select_related("wholesaler")

    # Filter by specific wholesaler if provided
    if wholesaler_id is not None:
        items_queryset = items_queryset.filter(wholesaler_id=wholesaler_id)

    # Filter by region if provided
    if region_id is not None:
        region_ids = _get_region_hierarchy(region_id)
        if region_ids:
            items_queryset = items_queryset.filter(
                wholesaler__region_min_charge__region_id__in=region_ids,
                wholesaler__region_min_charge__deleted_at__isnull=True,
            )

    # Order by price to get lowest first
    items = items_queryset.order_by("base_price")

    if not items:
        return {
            **product_data,
            "lowest_price": None,
            "lowest_price_wholesaler": None,
            "other_prices": [],
            "price_range": None,
        }

    # Get the lowest price item
    lowest_price_item = items[0]
    lowest_price_wholesaler = WholesalerOutSchema(
        id=lowest_price_item.wholesaler.id,
        category=lowest_price_item.wholesaler.category,
        title=lowest_price_item.wholesaler.title,
        username=lowest_price_item.wholesaler.username,
        logo_url=lowest_price_item.wholesaler.logo.url
        if lowest_price_item.wholesaler.logo
        else None,
        background_image_url=lowest_price_item.wholesaler.background_image.url
        if lowest_price_item.wholesaler.background_image
        else None,
        created_at=lowest_price_item.wholesaler.created_at,
    )

    # Collect all prices for price range calculation
    all_prices = [item.base_price for item in items]
    price_range = (
        {"min": min(all_prices), "max": max(all_prices)} if all_prices else None
    )

    # Prepare other prices (all except the lowest one if there are multiple)
    other_prices = []
    for item in items:
        wholesaler_schema = WholesalerOutSchema(
            id=item.wholesaler.id,
            category=item.wholesaler.category,
            title=item.wholesaler.title,
            username=item.wholesaler.username,
            logo_url=item.wholesaler.logo.url if item.wholesaler.logo else None,
            background_image_url=item.wholesaler.background_image.url
            if item.wholesaler.background_image
            else None,
            created_at=item.wholesaler.created_at,
        )

        # Create a simple price info structure
        other_prices.append(
            {
                "price": item.base_price,
                "wholesaler": wholesaler_schema,
                "inventory_count": item.inventory_count,
                "price_expiry": item.price_expiry,
            }
        )

    return {
        **product_data,
        "lowest_price": lowest_price_item.base_price,
        "lowest_price_wholesaler": lowest_price_wholesaler,
        "other_prices": other_prices,
        "price_range": price_range,
    }


# GET: /api/v2/home/<USER>
def get_products(
    request,
    page: int = 1,
    page_size: int = 20,
    region_id: Optional[int] = None,
    wholesaler_id: Optional[int] = None,
    search: Optional[str] = None,
    category_id: Optional[int] = None,
    company_id: Optional[int] = None,
) -> PaginatedProductResponse:
    """
    Get products with pagination and filtering support.

    Query Parameters:
    - page: Page number (default: 1)
    - page_size: Number of items per page (default: 20, max: 100)
    - region_id: Filter by region ID (includes parent regions)
    - wholesaler_id: Filter by specific wholesaler ID
    - search: Search term for product name/title/description
    - category_id: Filter by category ID
    - company_id: Filter by company ID

    Example URLs:
    - /api/v2/home/<USER>
    - /api/v2/home/<USER>
    - /api/v2/home/<USER>

    Note: When wholesaler_id is provided, only that wholesaler's pricing will be returned.
    """
    try:
        # Validate and set defaults
        page = max(1, page or 1)
        page_size = min(100, max(1, page_size or 20))  # Limit max page size to 100

        # Create cache key based on all parameters
        cache_key = f"products_list_{region_id}_{wholesaler_id}_{search}_{category_id}_{company_id}_{page}_{page_size}"

        if not settings.DEBUG:
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result

        # Get filtered products
        products_queryset = _get_filtered_products(
            region_id=region_id,
            wholesaler_id=wholesaler_id,
            search=search,
            category_id=category_id,
            company_id=company_id,
        )

        # Apply pagination
        paginator = Paginator(products_queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert products to schema and enrich with pricing
        enriched_products = []
        for product in page_obj.object_list:
            product_data = _product_to_schema(product)

            # Get pricing information considering region and wholesaler filters
            enriched_product = _get_product_pricing_by_region(
                product_data, region_id=region_id, wholesaler_id=wholesaler_id
            )

            # Only include products that have at least one price
            if enriched_product.get("lowest_price") is not None:
                # Convert to the schema format expected by PaginatedProductResponse
                product_with_pricing = ProductWithPricing(
                    id=enriched_product["id"],
                    name=enriched_product["name"],
                    title=enriched_product["title"],
                    barcode=enriched_product["barcode"],
                    slug=enriched_product["slug"],
                    description=enriched_product["description"],
                    image_url=enriched_product["image_url"],
                    company_id=enriched_product.get("company", {}).get("id")
                    if enriched_product.get("company")
                    else None,
                    category_id=enriched_product.get("category", {}).get("id")
                    if enriched_product.get("category")
                    else None,
                    company=enriched_product.get("company"),
                    category=enriched_product.get("category"),
                    unit=enriched_product["unit"],
                    unit_count=enriched_product["unit_count"],
                    base_price=enriched_product.get("lowest_price"),
                    other_price=enriched_product.get("price_range", {}).get("max")
                    if enriched_product.get("price_range")
                    else None,
                )
                enriched_products.append(product_with_pricing)

        # Prepare response
        result = PaginatedProductResponse(
            products=enriched_products,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

        # Cache for 30 minutes
        if not settings.DEBUG:
            cache.set(cache_key, result, 60 * 30)

        return result

    except Exception as e:
        logger.exception(f"Get products error: {str(e)}")
        raise HttpError(500, "Internal server error")
