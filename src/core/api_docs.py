from typing import Dict, Any
from ninja import Ninja<PERSON><PERSON>
from ninja.openapi.schema import OpenAPISchema


def get_api_docs_by_tag(api: NinjaAPI, tag: str) -> Dict[str, Any]:
    """
    Get OpenAPI documentation for all endpoints with a specific tag.
    Includes request/response schemas used by those endpoints.
    """
    # Get the OpenAPI schema
    openapi_schema: OpenAPISchema = api.get_openapi_schema()

    # Filter paths to only include those with the specified tag
    filtered_paths = {}
    schemas = {}

    for path, methods in openapi_schema["paths"].items():
        for method, endpoint in methods.items():
            if method.lower() in ["get", "post", "put", "delete", "patch"]:
                tags = endpoint.get("tags", [])
                if tag in tags:
                    # Add the endpoint to filtered paths
                    if path not in filtered_paths:
                        filtered_paths[path] = {}
                    filtered_paths[path][method] = endpoint

                    # Track schemas used in this endpoint
                    self_ref = "#/components/schemas/"

                    # Check request body schemas
                    if "requestBody" in endpoint:
                        for content in (
                            endpoint["requestBody"].get("content", {}).values()
                        ):
                            if "schema" in content and "$ref" in content["schema"]:
                                schema_ref = content["schema"]["$ref"]
                                if schema_ref.startswith(self_ref):
                                    schema_name = schema_ref[len(self_ref) :]
                                    schemas[schema_name] = openapi_schema["components"][
                                        "schemas"
                                    ].get(schema_name, {})

                    # Check response schemas
                    for response in endpoint.get("responses", {}).values():
                        for content in response.get("content", {}).values():
                            if "schema" in content and "$ref" in content["schema"]:
                                schema_ref = content["schema"]["$ref"]
                                if schema_ref.startswith(self_ref):
                                    schema_name = schema_ref[len(self_ref) :]
                                    schemas[schema_name] = openapi_schema["components"][
                                        "schemas"
                                    ].get(schema_name, {})
                            elif (
                                "items" in content.get("schema", {})
                                and "$ref" in content["schema"]["items"]
                            ):
                                schema_ref = content["schema"]["items"]["$ref"]
                                if schema_ref.startswith(self_ref):
                                    schema_name = schema_ref[len(self_ref) :]
                                    schemas[schema_name] = openapi_schema["components"][
                                        "schemas"
                                    ].get(schema_name, {})

    # Prepare the response
    result = {"tag": tag, "paths": filtered_paths, "schemas": schemas}

    return result


def register_docs_endpoints(api: NinjaAPI):
    """Register documentation-related endpoints."""

    @api.get(
        "/docs/tag/{tag}",
        tags=["docs"],
        summary="Get API documentation for a specific tag",
    )
    def get_docs_by_tag(request, tag: str):
        """
        Get OpenAPI documentation for all endpoints with a specific tag,
        including the request/response schemas they use.
        """
        return get_api_docs_by_tag(api, tag)
