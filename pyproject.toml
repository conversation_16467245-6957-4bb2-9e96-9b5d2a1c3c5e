[project]
name = "api-django"
version = "0.1.0"
description = "Add your description here"
authors = [{ name = "SeifAlmotaz", email = "<EMAIL>" }]
dependencies = [
    "django>=5.2",
    "django-extensions>=3.2.3",
    "pillow>=11.1.0",
    "django-ninja>=1.3.0",
    "whitenoise>=6.9.0",
    "django-safedelete>=1.4.1",
    "dotenv>=0.9.9",
    "uvicorn>=0.34.0",
    "psycopg2-binary>=2.9.10",
    "django-cors-headers>=4.7.0",
    "django-storages[s3]>=1.14.6",
    "boto3>=1.37.27",
    "pyjwt>=2.10.1",
    "pydantic[email]>=2.11.2",
    "httpx>=0.28.1",
    "redis>=5.2.1",
    "django-redis>=5.4.0",
    "openai>=1.75.0",
    "upstash-vector>=0.8.0",
    "factory-boy>=3.3.3",
    "firebase-admin>=6.9.0",
    "django-hosts>=7.0.0",
    "pgvector>=0.4.1",
    "sentry-sdk[django]>=2.34.1",
]
readme = "README.md"
requires-python = ">= 3.12"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.rye]
managed = true
virtual = true
dev-dependencies = []

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build.targets.wheel]
packages = ["src/api_django"]
