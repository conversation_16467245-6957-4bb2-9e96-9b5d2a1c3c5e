# generated by rye
# use `rye lock` or `rye sync` to update this lockfile
#
# last locked with the following flags:
#   pre: false
#   features: []
#   all-features: false
#   with-sources: false
#   generate-hashes: false
#   universal: false

annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via httpx
    # via openai
asgiref==3.8.1
    # via django
    # via django-cors-headers
boto3==1.37.27
    # via django-storages
botocore==1.37.27
    # via boto3
    # via s3transfer
cachecontrol==0.14.3
    # via firebase-admin
cachetools==5.5.2
    # via google-auth
certifi==2025.1.31
    # via httpcore
    # via httpx
    # via requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.2
    # via requests
click==8.1.8
    # via uvicorn
cryptography==45.0.5
    # via pyjwt
distro==1.9.0
    # via openai
django==5.2
    # via django-cors-headers
    # via django-extensions
    # via django-ninja
    # via django-redis
    # via django-safedelete
    # via django-storages
django-cors-headers==4.7.0
django-extensions==3.2.3
django-ninja==1.3.0
django-redis==5.4.0
django-safedelete==1.4.1
django-storages==1.14.6
dnspython==2.7.0
    # via email-validator
dotenv==0.9.9
email-validator==2.2.0
    # via pydantic
factory-boy==3.3.3
faker==37.4.0
    # via factory-boy
firebase-admin==6.9.0
google-api-core==2.25.1
    # via firebase-admin
    # via google-api-python-client
    # via google-cloud-core
    # via google-cloud-firestore
    # via google-cloud-storage
google-api-python-client==2.175.0
    # via firebase-admin
google-auth==2.40.3
    # via google-api-core
    # via google-api-python-client
    # via google-auth-httplib2
    # via google-cloud-core
    # via google-cloud-firestore
    # via google-cloud-storage
google-auth-httplib2==0.2.0
    # via google-api-python-client
google-cloud-core==2.4.3
    # via google-cloud-firestore
    # via google-cloud-storage
google-cloud-firestore==2.21.0
    # via firebase-admin
google-cloud-storage==3.1.1
    # via firebase-admin
google-crc32c==1.7.1
    # via google-cloud-storage
    # via google-resumable-media
google-resumable-media==2.7.2
    # via google-cloud-storage
googleapis-common-protos==1.70.0
    # via google-api-core
    # via grpcio-status
grpcio==1.73.1
    # via google-api-core
    # via grpcio-status
grpcio-status==1.73.1
    # via google-api-core
h11==0.14.0
    # via httpcore
    # via uvicorn
h2==4.2.0
    # via httpx
hpack==4.1.0
    # via h2
httpcore==1.0.8
    # via httpx
httplib2==0.22.0
    # via google-api-python-client
    # via google-auth-httplib2
httpx==0.28.1
    # via firebase-admin
    # via openai
    # via upstash-vector
hyperframe==6.1.0
    # via h2
idna==3.10
    # via anyio
    # via email-validator
    # via httpx
    # via requests
jiter==0.9.0
    # via openai
jmespath==1.0.1
    # via boto3
    # via botocore
msgpack==1.1.1
    # via cachecontrol
openai==1.75.0
packaging==24.2
    # via django-safedelete
pillow==11.1.0
proto-plus==1.26.1
    # via google-api-core
    # via google-cloud-firestore
protobuf==6.31.1
    # via google-api-core
    # via google-cloud-firestore
    # via googleapis-common-protos
    # via grpcio-status
    # via proto-plus
psycopg2-binary==2.9.10
pyasn1==0.6.1
    # via pyasn1-modules
    # via rsa
pyasn1-modules==0.4.2
    # via google-auth
pycparser==2.22
    # via cffi
pydantic==2.11.2
    # via django-ninja
    # via openai
pydantic-core==2.33.1
    # via pydantic
pyjwt==2.10.1
    # via firebase-admin
pyparsing==3.2.3
    # via httplib2
python-dateutil==2.9.0.post0
    # via botocore
python-dotenv==1.1.0
    # via dotenv
redis==5.2.1
    # via django-redis
requests==2.32.4
    # via cachecontrol
    # via google-api-core
    # via google-cloud-storage
rsa==4.9.1
    # via google-auth
s3transfer==0.11.4
    # via boto3
six==1.17.0
    # via python-dateutil
sniffio==1.3.1
    # via anyio
    # via openai
sqlparse==0.5.3
    # via django
tqdm==4.67.1
    # via openai
typing-extensions==4.13.0
    # via anyio
    # via openai
    # via pydantic
    # via pydantic-core
    # via typing-inspection
typing-inspection==0.4.0
    # via pydantic
tzdata==2025.2
    # via faker
upstash-vector==0.8.0
uritemplate==4.2.0
    # via google-api-python-client
urllib3==2.3.0
    # via botocore
    # via requests
uvicorn==0.34.0
whitenoise==6.9.0
